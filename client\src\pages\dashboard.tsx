import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Sidebar } from "@/components/sidebar";
import { StatsCards } from "@/components/stats-cards";
import { ActivityFeed } from "@/components/activity-feed";
import { VideoScheduler } from "@/components/video-scheduler";
import { CommentManagement } from "@/components/comment-management";
import { AiAutomation } from "@/components/ai-automation";
import { Bell, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

type ActiveSection = "dashboard" | "videos" | "comments" | "automation" | "analytics" | "settings";

export default function Dashboard() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [activeSection, setActiveSection] = useState<ActiveSection>("dashboard");
  const [selectedChannelId, setSelectedChannelId] = useState<number | null>(null);

  // Fetch user's channels
  const { data: channels = [] } = useQuery({
    queryKey: ["/api/channels"],
    enabled: !!user,
  });

  // YouTube channel connection mutation
  const connectChannelMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("POST", "/api/channels/connect");
      return res.json();
    },
    onSuccess: (data) => {
      if (data.authUrl) {
        const popup = window.open(data.authUrl, '_blank', 'width=600,height=600');
        toast({
          title: "YouTube Authorization",
          description: "Please complete the authorization in the popup window.",
        });

        // Listen for the popup callback
        const handleMessage = (event: MessageEvent) => {
          if (event.data.type === 'youtube_auth_success' && event.data.code) {
            window.removeEventListener('message', handleMessage);
            completeConnectionMutation.mutate(event.data.code);
          }
        };

        window.addEventListener('message', handleMessage);

        // Clean up if popup is closed manually
        const checkClosed = setInterval(() => {
          if (popup?.closed) {
            clearInterval(checkClosed);
            window.removeEventListener('message', handleMessage);
          }
        }, 1000);
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Connection failed",
        description: "YouTube API credentials are required. Please contact support to set up YouTube integration.",
        variant: "destructive",
      });
    },
  });

  // Complete YouTube channel connection
  const completeConnectionMutation = useMutation({
    mutationFn: async (code: string) => {
      const res = await apiRequest("POST", "/api/channels/complete", { code });
      return res.json();
    },
    onSuccess: (channel) => {
      queryClient.invalidateQueries({ queryKey: ["/api/channels"] });
      toast({
        title: "Channel connected successfully!",
        description: `YouTube channel "${channel.channelTitle}" has been connected.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Connection failed",
        description: error.message || "Failed to complete channel connection.",
        variant: "destructive",
      });
    },
  });

  // Set first channel as default if none selected
  if (channels.length > 0 && selectedChannelId === null) {
    setSelectedChannelId(channels[0].id);
  }

  const renderMainContent = () => {
    if (!selectedChannelId) {
      return (
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">No YouTube Channel Connected</h2>
          <p className="text-gray-600 mb-6">Connect your YouTube channel to get started with automation.</p>
          <Button 
            className="bg-red-600 hover:bg-red-700"
            onClick={() => connectChannelMutation.mutate()}
            disabled={connectChannelMutation.isPending}
          >
            <Plus className="h-4 w-4 mr-2" />
            {connectChannelMutation.isPending ? "Connecting..." : "Connect YouTube Channel"}
          </Button>
        </div>
      );
    }

    switch (activeSection) {
      case "dashboard":
        return (
          <div className="space-y-8">
            <StatsCards channelId={selectedChannelId} />
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <ActivityFeed channelId={selectedChannelId} />
              </div>
              <div className="space-y-6">
                {/* Quick Actions will be added here */}
              </div>
            </div>
          </div>
        );
      case "videos":
        return <VideoScheduler channelId={selectedChannelId} />;
      case "comments":
        return <CommentManagement channelId={selectedChannelId} />;
      case "automation":
        return <AiAutomation channelId={selectedChannelId} />;
      default:
        return <div className="text-center py-12">Feature coming soon...</div>;
    }
  };

  const getPageTitle = () => {
    switch (activeSection) {
      case "dashboard":
        return "Dashboard";
      case "videos":
        return "Video Scheduler";
      case "comments":
        return "Comment Management";
      case "automation":
        return "AI Response Configuration";
      case "analytics":
        return "Analytics";
      case "settings":
        return "Settings";
      default:
        return "Dashboard";
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar 
        activeSection={activeSection} 
        onSectionChange={setActiveSection}
        selectedChannelId={selectedChannelId}
        onChannelChange={setSelectedChannelId}
        channels={channels}
      />
      
      <main className="flex-1 overflow-auto">
        <div className="p-8">
          {/* Header */}
          <header className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{getPageTitle()}</h1>
              <p className="text-gray-600 mt-1">
                Welcome back! Here's what's happening with your channels.
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-600 text-white text-xs rounded-full flex items-center justify-center">
                  3
                </span>
              </Button>
              <Button 
                className="bg-red-600 hover:bg-red-700"
                onClick={() => connectChannelMutation.mutate()}
                disabled={connectChannelMutation.isPending}
              >
                <Plus className="h-4 w-4 mr-2" />
                {connectChannelMutation.isPending ? "Connecting..." : "Add Channel"}
              </Button>
            </div>
          </header>

          {/* Main content */}
          {renderMainContent()}
        </div>
      </main>
    </div>
  );
}
