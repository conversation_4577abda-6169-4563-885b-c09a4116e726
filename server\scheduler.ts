import cron from "node-cron";
import { storage } from "./storage";
import { youtube } from "./youtube";
import { processAutomaticResponses } from "./ai";
import { log } from "./vite";

class SchedulerService {
  private isRunning = false;

  start() {
    if (this.isRunning) {
      log("Scheduler is already running", "scheduler");
      return;
    }

    this.isRunning = true;
    log("Starting scheduler service", "scheduler");

    // Process scheduled videos every minute
    cron.schedule("* * * * *", async () => {
      try {
        await this.processScheduledVideos();
      } catch (error) {
        console.error("Error processing scheduled videos:", error);
      }
    });

    // Sync comments every 15 minutes
    const commentInterval = parseInt(process.env.COMMENT_SYNC_INTERVAL || "15");
    cron.schedule(`*/${commentInterval} * * * *`, async () => {
      try {
        await this.syncAllChannelComments();
      } catch (error) {
        console.error("Error syncing comments:", error);
      }
    });

    // Process automatic AI responses every 5 minutes
    cron.schedule("*/5 * * * *", async () => {
      try {
        await this.processAllAutomaticResponses();
      } catch (error) {
        console.error("Error processing automatic responses:", error);
      }
    });

    log("Scheduler service started successfully", "scheduler");
  }

  stop() {
    this.isRunning = false;
    log("Scheduler service stopped", "scheduler");
  }

  private async processScheduledVideos() {
    try {
      // Get all channels to check for scheduled videos
      const channels = await storage.getAllChannels();
      
      for (const channel of channels) {
        const scheduledVideos = await storage.getScheduledVideosByChannelId(channel.id);
        const videosToProcess = scheduledVideos.filter(video => 
          video.status === "scheduled" && 
          new Date(video.scheduledAt) <= new Date()
        );

        for (const video of videosToProcess) {
          try {
            await this.processVideo(video, channel);
          } catch (error) {
            console.error(`Error processing video ${video.id}:`, error);
            await storage.updateScheduledVideo(video.id, {
              status: "failed"
            });
            
            await storage.createActivityLog({
              channelId: channel.id,
              type: "video_failed",
              description: `Failed to publish video "${video.title}": ${error.message}`,
              metadata: { videoId: video.id, error: error.message }
            });
          }
        }
      }
    } catch (error) {
      console.error("Error in processScheduledVideos:", error);
    }
  }

  private async processVideo(video: any, channel: any) {
    log(`Processing scheduled video: ${video.title}`, "scheduler");

    // Update status to processing
    await storage.updateScheduledVideo(video.id, {
      status: "processing"
    });

    // Check if video file exists
    if (!video.videoFile) {
      throw new Error("No video file specified");
    }

    // Upload video to YouTube
    const tags = video.tags ? video.tags.split(",").map((tag: string) => tag.trim()) : [];
    
    const youtubeVideoId = await youtube.uploadVideo(channel, {
      title: video.title,
      description: video.description || "",
      tags,
      privacyStatus: video.privacy,
      videoFile: video.videoFile
    });

    // Update video record with YouTube video ID
    await storage.updateScheduledVideo(video.id, {
      status: "published",
      youtubeVideoId,
      publishedAt: new Date()
    });

    // Log the successful publication
    await storage.createActivityLog({
      channelId: channel.id,
      type: "video_published",
      description: `Video "${video.title}" published successfully`,
      metadata: { 
        videoId: video.id, 
        youtubeVideoId,
        title: video.title 
      }
    });

    log(`Successfully published video: ${video.title}`, "scheduler");
  }

  private async syncAllChannelComments() {
    try {
      const channels = await storage.getAllChannels();
      
      for (const channel of channels) {
        if (!channel.isActive) continue;
        
        try {
          const newComments = await youtube.syncComments(channel);
          
          if (newComments.length > 0) {
            log(`Synced ${newComments.length} new comments for channel ${channel.channelTitle}`, "scheduler");
            
            await storage.createActivityLog({
              channelId: channel.id,
              type: "comments_synced",
              description: `Automatically synced ${newComments.length} new comments`,
              metadata: { count: newComments.length }
            });
          }
        } catch (error) {
          console.error(`Error syncing comments for channel ${channel.id}:`, error);
        }
      }
    } catch (error) {
      console.error("Error in syncAllChannelComments:", error);
    }
  }

  private async processAllAutomaticResponses() {
    try {
      const channels = await storage.getAllChannels();
      
      for (const channel of channels) {
        if (!channel.isActive) continue;
        
        try {
          await processAutomaticResponses(channel.id);
        } catch (error) {
          console.error(`Error processing automatic responses for channel ${channel.id}:`, error);
        }
      }
    } catch (error) {
      console.error("Error in processAllAutomaticResponses:", error);
    }
  }
}

export const scheduler = new SchedulerService();
