{"version": "6", "dialect": "sqlite", "id": "3532ca29-bf8c-4961-a49c-367d6210346f", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"activity_logs": {"name": "activity_logs", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "channel_id": {"name": "channel_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'CURRENT_TIMESTAMP'"}}, "indexes": {}, "foreignKeys": {"activity_logs_channel_id_youtube_channels_id_fk": {"name": "activity_logs_channel_id_youtube_channels_id_fk", "tableFrom": "activity_logs", "tableTo": "youtube_channels", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "ai_response_rules": {"name": "ai_response_rules", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "channel_id": {"name": "channel_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "trigger_type": {"name": "trigger_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "triggers": {"name": "triggers", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "response": {"name": "response", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "confidence_threshold": {"name": "confidence_threshold", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 85}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "success_rate": {"name": "success_rate", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'CURRENT_TIMESTAMP'"}}, "indexes": {}, "foreignKeys": {"ai_response_rules_channel_id_youtube_channels_id_fk": {"name": "ai_response_rules_channel_id_youtube_channels_id_fk", "tableFrom": "ai_response_rules", "tableTo": "youtube_channels", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "comment_replies": {"name": "comment_replies", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "comment_id": {"name": "comment_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "youtube_reply_id": {"name": "youtube_reply_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_ai_generated": {"name": "is_ai_generated", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'CURRENT_TIMESTAMP'"}}, "indexes": {}, "foreignKeys": {"comment_replies_comment_id_comments_id_fk": {"name": "comment_replies_comment_id_comments_id_fk", "tableFrom": "comment_replies", "tableTo": "comments", "columnsFrom": ["comment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "comments": {"name": "comments", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "channel_id": {"name": "channel_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "video_id": {"name": "video_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "youtube_comment_id": {"name": "youtube_comment_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "author_name": {"name": "author_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "author_avatar": {"name": "author_avatar", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "like_count": {"name": "like_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "published_at": {"name": "published_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "sentiment": {"name": "sentiment", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sentiment_score": {"name": "sentiment_score", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'unread'"}, "parent_comment_id": {"name": "parent_comment_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'CURRENT_TIMESTAMP'"}}, "indexes": {"comments_youtube_comment_id_unique": {"name": "comments_youtube_comment_id_unique", "columns": ["youtube_comment_id"], "isUnique": true}}, "foreignKeys": {"comments_channel_id_youtube_channels_id_fk": {"name": "comments_channel_id_youtube_channels_id_fk", "tableFrom": "comments", "tableTo": "youtube_channels", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "scheduled_videos": {"name": "scheduled_videos", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "channel_id": {"name": "channel_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "thumbnail_url": {"name": "thumbnail_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "video_file": {"name": "video_file", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "youtube_video_id": {"name": "youtube_video_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "scheduled_at": {"name": "scheduled_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "published_at": {"name": "published_at", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'scheduled'"}, "privacy": {"name": "privacy", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'public'"}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'CURRENT_TIMESTAMP'"}}, "indexes": {}, "foreignKeys": {"scheduled_videos_channel_id_youtube_channels_id_fk": {"name": "scheduled_videos_channel_id_youtube_channels_id_fk", "tableFrom": "scheduled_videos", "tableTo": "youtube_channels", "columnsFrom": ["channel_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'CURRENT_TIMESTAMP'"}}, "indexes": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"], "isUnique": true}, "users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "youtube_channels": {"name": "youtube_channels", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "channel_id": {"name": "channel_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "channel_title": {"name": "channel_title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "subscriber_count": {"name": "subscriber_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'CURRENT_TIMESTAMP'"}}, "indexes": {"youtube_channels_channel_id_unique": {"name": "youtube_channels_channel_id_unique", "columns": ["channel_id"], "isUnique": true}}, "foreignKeys": {"youtube_channels_user_id_users_id_fk": {"name": "youtube_channels_user_id_users_id_fk", "tableFrom": "youtube_channels", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}