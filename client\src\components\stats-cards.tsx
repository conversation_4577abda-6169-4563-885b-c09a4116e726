import { Eye, Users, Calendar, MessageCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";

interface StatsCardsProps {
  channelId: number;
}

export function StatsCards({ channelId }: StatsCardsProps) {
  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ["/api/dashboard", channelId],
    enabled: !!channelId,
  });

  const stats = dashboardData?.stats || {
    totalViews: 0,
    subscribers: 0,
    scheduledVideos: 0,
    pendingComments: 0,
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num.toString();
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <Skeleton className="h-4 w-20 mb-2" />
              <Skeleton className="h-8 w-16 mb-4" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const statsData = [
    {
      title: "Total Views",
      value: formatNumber(stats.totalViews),
      icon: Eye,
      change: "+12.3%",
      changeType: "positive" as const,
      bgColor: "bg-blue-100",
      iconColor: "text-blue-600",
    },
    {
      title: "Subscribers", 
      value: formatNumber(stats.subscribers),
      icon: Users,
      change: "+8.7%",
      changeType: "positive" as const,
      bgColor: "bg-red-100",
      iconColor: "text-red-600",
    },
    {
      title: "Scheduled Videos",
      value: stats.scheduledVideos.toString(),
      icon: Calendar,
      change: dashboardData?.upcomingVideos?.[0] ? "Next: Tomorrow 2:00 PM" : "No videos scheduled",
      changeType: "neutral" as const,
      bgColor: "bg-green-100",
      iconColor: "text-green-600",
    },
    {
      title: "Pending Comments",
      value: stats.pendingComments.toString(),
      icon: MessageCircle,
      change: stats.pendingComments > 0 ? "Needs attention" : "All caught up",
      changeType: stats.pendingComments > 0 ? "warning" : "neutral" as const,
      bgColor: "bg-orange-100",
      iconColor: "text-orange-600",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statsData.map((stat, index) => {
        const Icon = stat.icon;
        
        return (
          <Card key={index} className="border border-gray-100">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm font-medium">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-2">{stat.value}</p>
                </div>
                <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                  <Icon className={`h-6 w-6 ${stat.iconColor}`} />
                </div>
              </div>
              <div className="flex items-center mt-4">
                <span className={`text-sm font-medium ${
                  stat.changeType === "positive" 
                    ? "text-green-600" 
                    : stat.changeType === "warning" 
                    ? "text-orange-600" 
                    : "text-gray-600"
                }`}>
                  {stat.change}
                </span>
                {stat.changeType === "positive" && (
                  <span className="text-gray-600 text-sm ml-1">vs last month</span>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
