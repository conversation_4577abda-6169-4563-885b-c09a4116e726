# YouTube Automation Platform

A comprehensive YouTube channel management and automation platform built with React, TypeScript, Node.js, and PostgreSQL.

## Features

- **YouTube Channel Integration**: Connect multiple YouTube channels via OAuth
- **Video Scheduling**: Upload and schedule videos for automatic publication
- **Comment Management**: Centralized comment management with sentiment analysis
- **AI-Powered Responses**: Automated comment responses using OpenAI
- **Real-time Dashboard**: Analytics and activity monitoring
- **File Upload**: Support for video and thumbnail uploads

## Tech Stack

- **Frontend**: React 18, TypeScript, TailwindCSS, shadcn/ui
- **Backend**: Node.js, Express, TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **APIs**: YouTube Data API v3, OpenAI API
- **File Upload**: Multer for handling video/image uploads
- **Scheduling**: node-cron for background tasks

## Prerequisites

- Node.js 20+
- PostgreSQL database
- Google Cloud Console project with YouTube Data API v3 enabled
- OpenAI API key

## Setup Instructions

### 1. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Fill in the required environment variables:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/youtube_automation

# YouTube API (from Google Cloud Console)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Session
SESSION_SECRET=your_secure_random_string

# Replit (if deploying on Replit)
REPLIT_DEV_DOMAIN=your-repl-name.your-username.repl.co
```

### 2. Database Setup

Push the database schema:

```bash
npm run db:push
```

### 3. Google Cloud Console Setup

1. Create a new project in [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the YouTube Data API v3
3. Create OAuth 2.0 credentials
4. Add authorized redirect URIs:
   - `http://localhost:5000/api/channels/callback` (development)
   - `https://your-domain.com/api/channels/callback` (production)

### 4. Install Dependencies

```bash
npm install
```

### 5. Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:5000`

## Usage

### 1. User Registration
- Create an account or log in
- Access the dashboard

### 2. Connect YouTube Channel
- Click "Connect YouTube Channel"
- Complete OAuth authorization
- Your channel will appear in the sidebar

### 3. Schedule Videos
- Go to "Video Scheduler"
- Upload video file and optional thumbnail
- Fill in video details (title, description, tags)
- Set publication date/time
- Choose privacy settings
- Click "Schedule Video"

### 4. Manage Comments
- Go to "Comment Management"
- Sync comments from YouTube
- View sentiment analysis
- Reply to comments manually or set up AI automation

### 5. AI Automation
- Go to "AI Response Configuration"
- Create response rules based on keywords or sentiment
- Enable automatic responses

## API Endpoints

### Authentication
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `POST /api/logout` - User logout
- `GET /api/user` - Get current user

### YouTube Channels
- `GET /api/channels` - Get user's channels
- `POST /api/channels/connect` - Initiate YouTube OAuth
- `POST /api/channels/complete` - Complete channel connection

### Video Management
- `GET /api/videos/:channelId` - Get scheduled videos
- `POST /api/videos` - Schedule new video
- `PUT /api/videos/:id` - Update scheduled video
- `DELETE /api/videos/:id` - Delete scheduled video

### File Upload
- `POST /api/upload/video` - Upload video file
- `POST /api/upload/thumbnail` - Upload thumbnail

### Comments
- `GET /api/comments/:channelId` - Get channel comments
- `POST /api/comments/sync/:channelId` - Sync comments from YouTube
- `POST /api/comments/:id/reply` - Reply to comment

## Background Tasks

The application runs several background tasks:

- **Video Processing**: Checks every minute for videos to publish
- **Comment Sync**: Syncs new comments every 15 minutes
- **AI Responses**: Processes automatic responses every 5 minutes

## File Structure

```
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   └── lib/            # Utilities
├── server/                 # Node.js backend
│   ├── ai.ts              # AI/OpenAI integration
│   ├── auth.ts            # Authentication
│   ├── db.ts              # Database connection
│   ├── routes.ts          # API routes
│   ├── scheduler.ts       # Background tasks
│   ├── storage.ts         # Database operations
│   ├── upload.ts          # File upload handling
│   └── youtube.ts         # YouTube API integration
├── shared/                 # Shared types and schemas
└── uploads/               # File upload directory
```

## Deployment

### Production Build

```bash
npm run build
npm start
```

### Environment Variables for Production

Ensure all environment variables are properly set for production, especially:
- `NODE_ENV=production`
- `DATABASE_URL` (production database)
- `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET`
- `OPENAI_API_KEY`
- `SESSION_SECRET` (use a strong, random string)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License
