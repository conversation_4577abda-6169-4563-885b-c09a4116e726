import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { Search, RefreshCw, Heart, ThumbsUp, MessageCircle, Flag } from "lucide-react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";

interface CommentManagementProps {
  channelId: number;
}

export function CommentManagement({ channelId }: CommentManagementProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyText, setReplyText] = useState("");

  const { data: comments = [], isLoading } = useQuery({
    queryKey: ["/api/comments", channelId],
    enabled: !!channelId,
  });

  const syncCommentsMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("POST", `/api/comments/sync/${channelId}`);
      return res.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Comments synced",
        description: `Synced ${data.synced} new comments from YouTube.`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/comments", channelId] });
      queryClient.invalidateQueries({ queryKey: ["/api/dashboard", channelId] });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to sync comments",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const replyMutation = useMutation({
    mutationFn: async ({ commentId, text }: { commentId: number; text: string }) => {
      const res = await apiRequest("POST", `/api/comments/${commentId}/reply`, {
        text,
        isAiGenerated: false
      });
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Reply posted",
        description: "Your reply has been posted to YouTube.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/comments", channelId] });
      setReplyingTo(null);
      setReplyText("");
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to post reply",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateCommentMutation = useMutation({
    mutationFn: async ({ commentId, updates }: { commentId: number; updates: any }) => {
      const res = await apiRequest("PUT", `/api/comments/${commentId}`, updates);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/comments", channelId] });
    },
  });

  const filteredComments = comments.filter(comment => {
    const matchesSearch = comment.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         comment.authorName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || comment.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleReply = (commentId: number) => {
    if (!replyText.trim()) return;
    replyMutation.mutate({ commentId, text: replyText });
  };

  const markAsRead = (commentId: number) => {
    updateCommentMutation.mutate({
      commentId,
      updates: { status: "read" }
    });
  };

  const getSentimentBadge = (sentiment: string | null, sentimentScore: number | null) => {
    if (!sentiment) return null;
    
    const colors = {
      positive: "bg-green-100 text-green-700",
      negative: "bg-red-100 text-red-700", 
      neutral: "bg-gray-100 text-gray-700"
    };

    return (
      <Badge variant="outline" className={colors[sentiment as keyof typeof colors]}>
        {sentiment} {sentimentScore && `(${sentimentScore}%)`}
      </Badge>
    );
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      unread: "bg-orange-100 text-orange-700",
      read: "bg-gray-100 text-gray-700",
      replied: "bg-green-100 text-green-700",
      flagged: "bg-red-100 text-red-700"
    };

    return (
      <Badge variant="outline" className={colors[status as keyof typeof colors]}>
        {status === "unread" ? "Needs response" : status}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search comments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="unread">Unread</SelectItem>
                  <SelectItem value="read">Read</SelectItem>
                  <SelectItem value="replied">Replied</SelectItem>
                  <SelectItem value="flagged">Flagged</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button 
              onClick={() => syncCommentsMutation.mutate()}
              disabled={syncCommentsMutation.isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${syncCommentsMutation.isPending ? "animate-spin" : ""}`} />
              Sync Comments
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Comments List */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Comments</CardTitle>
          <p className="text-sm text-gray-600">
            {filteredComments.filter(c => c.status === "unread").length} pending responses
          </p>
        </CardHeader>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="divide-y divide-gray-200">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="p-6">
                  <div className="flex items-start space-x-4">
                    <Skeleton className="w-10 h-10 rounded-full" />
                    <div className="flex-1">
                      <Skeleton className="h-4 w-32 mb-2" />
                      <Skeleton className="h-16 w-full mb-3" />
                      <div className="flex space-x-4">
                        <Skeleton className="h-6 w-16" />
                        <Skeleton className="h-6 w-16" />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredComments.length === 0 ? (
            <div className="text-center py-12">
              <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No comments found</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredComments.map((comment) => (
                <div key={comment.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start space-x-4">
                    <Avatar>
                      <AvatarImage src={comment.authorAvatar || ""} alt={comment.authorName} />
                      <AvatarFallback>{comment.authorName.slice(0, 2).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-medium text-gray-900">{comment.authorName}</h4>
                        <span className="text-gray-500 text-sm">
                          {formatDistanceToNow(new Date(comment.publishedAt), { addSuffix: true })}
                        </span>
                        <Badge variant="outline" className="text-blue-600 border-blue-600">
                          Video: {comment.videoId}
                        </Badge>
                      </div>
                      
                      <p className="text-gray-900 mb-3">{comment.text}</p>
                      
                      <div className="flex items-center space-x-4 mb-3">
                        <div className="flex items-center space-x-1 text-gray-500">
                          <Heart className="h-4 w-4" />
                          <span className="text-sm">{comment.likeCount}</span>
                        </div>
                        {getSentimentBadge(comment.sentiment, comment.sentimentScore)}
                        {getStatusBadge(comment.status)}
                      </div>

                      <div className="flex items-center space-x-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setReplyingTo(comment.id)}
                        >
                          Reply
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => markAsRead(comment.id)}
                        >
                          <ThumbsUp className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Flag className="h-4 w-4" />
                        </Button>
                      </div>

                      {replyingTo === comment.id && (
                        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <Textarea
                            placeholder="Write your reply..."
                            value={replyText}
                            onChange={(e) => setReplyText(e.target.value)}
                            className="mb-3"
                            rows={3}
                          />
                          <div className="flex space-x-2">
                            <Button
                              onClick={() => handleReply(comment.id)}
                              disabled={replyMutation.isPending || !replyText.trim()}
                              size="sm"
                              className="bg-blue-600 hover:bg-blue-700"
                            >
                              {replyMutation.isPending ? "Posting..." : "Post Reply"}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setReplyingTo(null);
                                setReplyText("");
                              }}
                            >
                              Cancel
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
