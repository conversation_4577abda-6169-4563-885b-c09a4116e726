@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 98%; /* #FAFAFA */
  --foreground: 0 0% 13%; /* #212121 */
  --muted: 0 0% 96%; /* #F5F5F5 */
  --muted-foreground: 0 0% 46%; /* #757575 */
  --popover: 0 0% 100%; /* #FFFFFF */
  --popover-foreground: 0 0% 13%; /* #212121 */
  --card: 0 0% 100%; /* #FFFFFF */
  --card-foreground: 0 0% 13%; /* #212121 */
  --border: 0 0% 90%; /* #E5E5E5 */
  --input: 0 0% 90%; /* #E5E5E5 */
  --primary: 0 100% 50%; /* #FF0000 - YouTube Red */
  --primary-foreground: 211 100% 99%; /* #FAFCFF */
  --secondary: 0 0% 96%; /* #F5F5F5 */
  --secondary-foreground: 0 0% 13%; /* #212121 */
  --accent: 0 0% 96%; /* #F5F5F5 */
  --accent-foreground: 0 0% 13%; /* #212121 */
  --destructive: 0 84% 60%; /* #F44336 */
  --destructive-foreground: 0 0% 98%; /* #FAFAFA */
  --ring: 0 0% 13%; /* #212121 */
  --radius: 0.5rem;

  /* Custom YouTube theme colors */
  --youtube: 0 100% 50%; /* #FF0000 */
  --youtube-dark: 0 100% 40%; /* #CC0000 */
  --primary-blue: 210 83% 53%; /* #1976D2 */
  --surface: 0 0% 98%; /* #FAFAFA */
  --text-primary: 0 0% 13%; /* #212121 */
  --text-secondary: 0 0% 46%; /* #757575 */
  --success: 122 39% 49%; /* #4CAF50 */
  --warning: 36 100% 50%; /* #FF9800 */
  --error: 4 90% 58%; /* #F44336 */
}

.dark {
  --background: 240 10% 4%; /* #0A0A0B */
  --foreground: 0 0% 98%; /* #FAFAFA */
  --muted: 240 4% 16%; /* #27272A */
  --muted-foreground: 240 5% 65%; /* #A1A1AA */
  --popover: 240 10% 4%; /* #0A0A0B */
  --popover-foreground: 0 0% 98%; /* #FAFAFA */
  --card: 240 10% 4%; /* #0A0A0B */
  --card-foreground: 0 0% 98%; /* #FAFAFA */
  --border: 240 4% 16%; /* #27272A */
  --input: 240 4% 16%; /* #27272A */
  --primary: 0 100% 50%; /* #FF0000 - YouTube Red */
  --primary-foreground: 211 100% 99%; /* #FAFCFF */
  --secondary: 240 4% 16%; /* #27272A */
  --secondary-foreground: 0 0% 98%; /* #FAFAFA */
  --accent: 240 4% 16%; /* #27272A */
  --accent-foreground: 0 0% 98%; /* #FAFAFA */
  --destructive: 0 63% 31%; /* #7F1D1D */
  --destructive-foreground: 0 0% 98%; /* #FAFAFA */
  --ring: 240 5% 84%; /* #D4D4D8 */

  /* Dark mode custom colors */
  --youtube: 0 100% 50%; /* #FF0000 */
  --youtube-dark: 0 100% 40%; /* #CC0000 */
  --primary-blue: 210 83% 53%; /* #1976D2 */
  --surface: 240 10% 4%; /* #0A0A0B */
  --text-primary: 0 0% 98%; /* #FAFAFA */
  --text-secondary: 0 0% 65%; /* #A5A5A5 */
  --success: 122 39% 49%; /* #4CAF50 */
  --warning: 36 100% 50%; /* #FF9800 */
  --error: 4 90% 58%; /* #F44336 */
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }

  /* YouTube specific styling */
  .bg-youtube {
    background-color: hsl(var(--youtube));
  }

  .bg-youtube-dark {
    background-color: hsl(var(--youtube-dark));
  }

  .text-youtube {
    color: hsl(var(--youtube));
  }

  .border-youtube {
    border-color: hsl(var(--youtube));
  }

  .bg-primary-blue {
    background-color: hsl(var(--primary-blue));
  }

  .text-primary-blue {
    color: hsl(var(--primary-blue));
  }

  .bg-surface {
    background-color: hsl(var(--surface));
  }

  .text-text-primary {
    color: hsl(var(--text-primary));
  }

  .text-text-secondary {
    color: hsl(var(--text-secondary));
  }

  .bg-success {
    background-color: hsl(var(--success));
  }

  .text-success {
    color: hsl(var(--success));
  }

  .bg-warning {
    background-color: hsl(var(--warning));
  }

  .text-warning {
    color: hsl(var(--warning));
  }

  .bg-error {
    background-color: hsl(var(--error));
  }

  .text-error {
    color: hsl(var(--error));
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground));
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--foreground));
  }

  /* Focus styles */
  .focus-visible:focus-visible {
    outline: 2px solid hsl(var(--youtube));
    outline-offset: 2px;
  }

  /* Button hover effects */
  .btn-youtube:hover {
    background-color: hsl(var(--youtube-dark));
  }

  /* Card hover effects */
  .card-hover:hover {
    box-shadow: 0 4px 12px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
    transition: all 0.2s ease-in-out;
  }

  /* Status indicators */
  .status-active::before {
    content: '';
    width: 8px;
    height: 8px;
    background-color: hsl(var(--success));
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
  }

  .status-inactive::before {
    content: '';
    width: 8px;
    height: 8px;
    background-color: hsl(var(--muted-foreground));
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
  }

  /* Notification badges */
  .notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background-color: hsl(var(--youtube));
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
  }

  /* Loading animations */
  .loading-shimmer {
    background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--background)) 50%, hsl(var(--muted)) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Progress bars */
  .progress-bar {
    background-color: hsl(var(--muted));
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-fill {
    background-color: hsl(var(--youtube));
    height: 100%;
    transition: width 0.3s ease-in-out;
  }

  /* Form input focus states */
  .form-input:focus {
    border-color: hsl(var(--youtube));
    box-shadow: 0 0 0 3px hsl(var(--youtube) / 0.1);
  }

  /* Sidebar navigation active states */
  .nav-item-active {
    background-color: hsl(var(--youtube) / 0.1);
    color: hsl(var(--youtube));
    border-color: hsl(var(--youtube) / 0.3);
  }

  /* Comment status indicators */
  .comment-unread {
    border-left: 4px solid hsl(var(--warning));
  }

  .comment-replied {
    border-left: 4px solid hsl(var(--success));
  }

  .comment-flagged {
    border-left: 4px solid hsl(var(--error));
  }

  /* AI response confidence indicators */
  .confidence-high {
    background-color: hsl(var(--success) / 0.1);
    color: hsl(var(--success));
  }

  .confidence-medium {
    background-color: hsl(var(--warning) / 0.1);
    color: hsl(var(--warning));
  }

  .confidence-low {
    background-color: hsl(var(--error) / 0.1);
    color: hsl(var(--error));
  }
}
