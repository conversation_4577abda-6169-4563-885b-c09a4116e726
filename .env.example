# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/youtube_automation

# YouTube API Configuration (Google Cloud Console)
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Session Configuration
SESSION_SECRET=your_secure_session_secret_here

# Replit Configuration (for OAuth callback)
REPLIT_DEV_DOMAIN=your-repl-name.your-username.repl.co

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=100000000

# Application Configuration
NODE_ENV=development
PORT=5000

# Scheduler Configuration
ENABLE_SCHEDULER=true
COMMENT_SYNC_INTERVAL=15
VIDEO_PROCESSING_INTERVAL=1

# Logging Configuration
LOG_LEVEL=info
