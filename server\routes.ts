import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { setupAuth } from "./auth";
import { youtube } from "./youtube";
import { analyzeSentiment, generateResponse } from "./ai";
import { uploadVideo, uploadThumbnail, getFilePath, deleteFile } from "./upload";
import { insertScheduledVideoSchema, insertCommentReplySchema, insertAiResponseRuleSchema } from "@shared/schema";

export function registerRoutes(app: Express): Server {
  // Authentication routes
  setupAuth(app);

  // Channel management routes
  app.get("/api/channels", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const channels = await storage.getChannelsByUserId(req.user!.id);
      res.json(channels);
    } catch (error) {
      console.error("Error fetching channels:", error);
      res.status(500).json({ message: "Failed to fetch channels" });
    }
  });

  app.post("/api/channels/connect", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      // TODO: Implement YouTube OAuth flow
      // This would redirect to YouTube OAuth consent screen
      const authUrl = youtube.getAuthUrl();
      res.json({ authUrl });
    } catch (error) {
      console.error("Error initiating YouTube connection:", error);
      res.status(500).json({ message: "Failed to initiate YouTube connection" });
    }
  });

  app.get("/api/channels/callback", async (req, res) => {
    // This handles the OAuth callback from Google
    try {
      const { code, state } = req.query;

      if (!code) {
        return res.status(400).send(`
          <html>
            <body>
              <h1>Errore di autorizzazione</h1>
              <p>Autorizzazione negata o codice mancante.</p>
              <script>window.close();</script>
            </body>
          </html>
        `);
      }

      // Store the code temporarily to be processed by the client
      // In a real app, you'd use session storage or state parameter
      res.send(`
        <html>
          <body>
            <h1>Autorizzazione completata!</h1>
            <p>Connettendo il tuo canale YouTube...</p>
            <script>
              // Send message to parent window with the authorization code
              if (window.opener) {
                window.opener.postMessage({
                  type: 'youtube_auth_success',
                  code: '${code}'
                }, '*');
                window.close();
              } else {
                // Fallback: redirect to main app
                window.location.href = '/';
              }
            </script>
          </body>
        </html>
      `);
    } catch (error) {
      console.error("Error in OAuth callback:", error);
      res.status(500).send(`
        <html>
          <body>
            <h1>Errore</h1>
            <p>Si è verificato un errore durante l'autorizzazione.</p>
            <script>window.close();</script>
          </body>
        </html>
      `);
    }
  });

  // Add endpoint to complete channel connection with the auth code
  app.post("/api/channels/complete", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const { code } = req.body;
      if (!code) {
        return res.status(400).json({ message: "Authorization code required" });
      }

      const channelData = await youtube.handleCallback(code, req.user!.id);
      const channel = await storage.createChannel(channelData);

      // Log the connection
      await storage.createActivityLog({
        channelId: channel.id,
        type: "channel_connected",
        description: `YouTube channel "${channel.channelTitle}" connected successfully`,
        metadata: { channelId: channel.channelId }
      });

      res.json(channel);
    } catch (error) {
      console.error("Error completing YouTube channel connection:", error);
      res.status(500).json({ message: "Failed to complete YouTube channel connection" });
    }
  });

  // Dashboard routes
  app.get("/api/dashboard/:channelId", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const channelId = parseInt(req.params.channelId);

      // Verify channel belongs to user
      const channel = await storage.getChannelById(channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Channel not found or access denied" });
      }

      const [stats, recentActivity, upcomingVideos] = await Promise.all([
        storage.getDashboardStats(channelId),
        storage.getActivityLogsByChannelId(channelId, 10),
        storage.getScheduledVideosByChannelId(channelId)
      ]);

      res.json({
        stats,
        recentActivity,
        upcomingVideos: upcomingVideos.slice(0, 5)
      });
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      res.status(500).json({ message: "Failed to fetch dashboard data" });
    }
  });

  // Video scheduling routes
  app.get("/api/videos/:channelId", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const channelId = parseInt(req.params.channelId);

      // Verify channel belongs to user
      const channel = await storage.getChannelById(channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Channel not found or access denied" });
      }

      const videos = await storage.getScheduledVideosByChannelId(channelId);
      res.json(videos);
    } catch (error) {
      console.error("Error fetching scheduled videos:", error);
      res.status(500).json({ message: "Failed to fetch scheduled videos" });
    }
  });

  app.post("/api/videos", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const videoData = insertScheduledVideoSchema.parse(req.body);

      // Verify channel belongs to user
      const channel = await storage.getChannelById(videoData.channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Channel not found or access denied" });
      }

      const video = await storage.createScheduledVideo(videoData);

      // Log the scheduling
      await storage.createActivityLog({
        channelId: video.channelId,
        type: "video_scheduled",
        description: `Video "${video.title}" scheduled for ${video.scheduledAt}`,
        metadata: { videoId: video.id, title: video.title }
      });

      res.json(video);
    } catch (error) {
      console.error("Error scheduling video:", error);
      res.status(500).json({ message: "Failed to schedule video" });
    }
  });

  app.put("/api/videos/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const videoId = parseInt(req.params.id);
      const updates = req.body;

      // Verify video exists and belongs to user's channel
      const video = await storage.getScheduledVideoById(videoId);
      if (!video) {
        return res.status(404).json({ message: "Video not found" });
      }

      const channel = await storage.getChannelById(video.channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedVideo = await storage.updateScheduledVideo(videoId, updates);
      res.json(updatedVideo);
    } catch (error) {
      console.error("Error updating video:", error);
      res.status(500).json({ message: "Failed to update video" });
    }
  });

  app.delete("/api/videos/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const videoId = parseInt(req.params.id);

      // Verify video exists and belongs to user's channel
      const video = await storage.getScheduledVideoById(videoId);
      if (!video) {
        return res.status(404).json({ message: "Video not found" });
      }

      const channel = await storage.getChannelById(video.channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      // Delete associated files
      if (video.videoFile) {
        try {
          await deleteFile(getFilePath(video.videoFile, 'video'));
        } catch (error) {
          console.error("Error deleting video file:", error);
        }
      }

      if (video.thumbnailUrl) {
        try {
          await deleteFile(getFilePath(video.thumbnailUrl, 'thumbnail'));
        } catch (error) {
          console.error("Error deleting thumbnail file:", error);
        }
      }

      await storage.deleteScheduledVideo(videoId);
      res.sendStatus(204);
    } catch (error) {
      console.error("Error deleting video:", error);
      res.status(500).json({ message: "Failed to delete video" });
    }
  });

  // File upload routes
  app.post("/api/upload/video", uploadVideo.single('video'), async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      if (!req.file) {
        return res.status(400).json({ message: "No video file uploaded" });
      }

      res.json({
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        path: req.file.path
      });
    } catch (error) {
      console.error("Error uploading video:", error);
      res.status(500).json({ message: "Failed to upload video" });
    }
  });

  app.post("/api/upload/thumbnail", uploadThumbnail.single('thumbnail'), async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      if (!req.file) {
        return res.status(400).json({ message: "No thumbnail file uploaded" });
      }

      res.json({
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        path: req.file.path
      });
    } catch (error) {
      console.error("Error uploading thumbnail:", error);
      res.status(500).json({ message: "Failed to upload thumbnail" });
    }
  });

  // Comment management routes
  app.get("/api/comments/:channelId", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const channelId = parseInt(req.params.channelId);
      const limit = parseInt(req.query.limit as string) || 50;

      // Verify channel belongs to user
      const channel = await storage.getChannelById(channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Channel not found or access denied" });
      }

      const comments = await storage.getCommentsByChannelId(channelId, limit);
      res.json(comments);
    } catch (error) {
      console.error("Error fetching comments:", error);
      res.status(500).json({ message: "Failed to fetch comments" });
    }
  });

  app.post("/api/comments/sync/:channelId", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const channelId = parseInt(req.params.channelId);

      // Verify channel belongs to user
      const channel = await storage.getChannelById(channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Channel not found or access denied" });
      }

      // Sync comments from YouTube
      const newComments = await youtube.syncComments(channel);

      // Analyze sentiment for new comments
      for (const comment of newComments) {
        try {
          const sentiment = await analyzeSentiment(comment.text);
          await storage.updateComment(comment.id, {
            sentiment: sentiment.rating > 3 ? "positive" : sentiment.rating < 3 ? "negative" : "neutral",
            sentimentScore: Math.round((sentiment.rating / 5) * 100)
          });
        } catch (error) {
          console.error("Error analyzing sentiment for comment:", comment.id, error);
        }
      }

      await storage.createActivityLog({
        channelId,
        type: "comments_synced",
        description: `Synced ${newComments.length} new comments from YouTube`,
        metadata: { count: newComments.length }
      });

      res.json({ synced: newComments.length });
    } catch (error) {
      console.error("Error syncing comments:", error);
      res.status(500).json({ message: "Failed to sync comments" });
    }
  });

  app.post("/api/comments/:id/reply", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const commentId = parseInt(req.params.id);
      const replyData = insertCommentReplySchema.parse(req.body);

      // Verify comment exists and belongs to user's channel
      const comment = await storage.getCommentById(commentId);
      if (!comment) {
        return res.status(404).json({ message: "Comment not found" });
      }

      const channel = await storage.getChannelById(comment.channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      // Post reply to YouTube
      const youtubeReplyId = await youtube.replyToComment(channel, comment.youtubeCommentId, replyData.text);

      // Save reply to database
      const reply = await storage.createCommentReply({
        ...replyData,
        commentId,
        youtubeReplyId
      });

      // Update comment status
      await storage.updateComment(commentId, { status: "replied" });

      res.json(reply);
    } catch (error) {
      console.error("Error posting reply:", error);
      res.status(500).json({ message: "Failed to post reply" });
    }
  });

  app.put("/api/comments/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const commentId = parseInt(req.params.id);
      const updates = req.body;

      // Verify comment exists and belongs to user's channel
      const comment = await storage.getCommentById(commentId);
      if (!comment) {
        return res.status(404).json({ message: "Comment not found" });
      }

      const channel = await storage.getChannelById(comment.channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedComment = await storage.updateComment(commentId, updates);
      res.json(updatedComment);
    } catch (error) {
      console.error("Error updating comment:", error);
      res.status(500).json({ message: "Failed to update comment" });
    }
  });

  // AI response rules routes
  app.get("/api/ai-rules/:channelId", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const channelId = parseInt(req.params.channelId);

      // Verify channel belongs to user
      const channel = await storage.getChannelById(channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Channel not found or access denied" });
      }

      const rules = await storage.getAiRulesByChannelId(channelId);
      res.json(rules);
    } catch (error) {
      console.error("Error fetching AI rules:", error);
      res.status(500).json({ message: "Failed to fetch AI rules" });
    }
  });

  app.post("/api/ai-rules", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const ruleData = insertAiResponseRuleSchema.parse(req.body);

      // Verify channel belongs to user
      const channel = await storage.getChannelById(ruleData.channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Channel not found or access denied" });
      }

      const rule = await storage.createAiRule(ruleData);
      res.json(rule);
    } catch (error) {
      console.error("Error creating AI rule:", error);
      res.status(500).json({ message: "Failed to create AI rule" });
    }
  });

  app.put("/api/ai-rules/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const ruleId = parseInt(req.params.id);
      const updates = req.body;

      // Verify rule exists and belongs to user's channel
      const rule = await storage.getAiRuleById(ruleId);
      if (!rule) {
        return res.status(404).json({ message: "AI rule not found" });
      }

      const channel = await storage.getChannelById(rule.channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedRule = await storage.updateAiRule(ruleId, updates);
      res.json(updatedRule);
    } catch (error) {
      console.error("Error updating AI rule:", error);
      res.status(500).json({ message: "Failed to update AI rule" });
    }
  });

  app.delete("/api/ai-rules/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const ruleId = parseInt(req.params.id);

      // Verify rule exists and belongs to user's channel
      const rule = await storage.getAiRuleById(ruleId);
      if (!rule) {
        return res.status(404).json({ message: "AI rule not found" });
      }

      const channel = await storage.getChannelById(rule.channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      await storage.deleteAiRule(ruleId);
      res.sendStatus(204);
    } catch (error) {
      console.error("Error deleting AI rule:", error);
      res.status(500).json({ message: "Failed to delete AI rule" });
    }
  });

  // AI response generation
  app.post("/api/ai/suggest-response", async (req, res) => {
    if (!req.isAuthenticated()) return res.sendStatus(401);

    try {
      const { commentText, channelId } = req.body;

      // Verify channel belongs to user
      const channel = await storage.getChannelById(channelId);
      if (!channel || channel.userId !== req.user!.id) {
        return res.status(403).json({ message: "Channel not found or access denied" });
      }

      const response = await generateResponse(commentText, channelId);
      res.json({ suggestedResponse: response });
    } catch (error) {
      console.error("Error generating AI response:", error);
      res.status(500).json({ message: "Failed to generate AI response" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
