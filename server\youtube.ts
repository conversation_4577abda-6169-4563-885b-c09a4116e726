import { google } from "googleapis";
import { storage } from "./storage";
import type { YoutubeChannel, InsertYoutubeChannel, Comment, InsertComment } from "@shared/schema";

const youtubeApi = google.youtube("v3");
const oauth2Client = new google.auth.OAuth2(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  `https://${process.env.REPLIT_DEV_DOMAIN || 'localhost:5000'}/api/channels/callback`
);

class YouTubeService {
  getAuthUrl(): string {
    const scopes = [
      "https://www.googleapis.com/auth/youtube.readonly",
      "https://www.googleapis.com/auth/youtube.force-ssl",
      "https://www.googleapis.com/auth/youtube.upload"
    ];

    return oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: scopes,
      prompt: "consent"
    });
  }

  async handleCallback(code: string, userId: number): Promise<InsertYoutubeChannel> {
    try {
      const { tokens } = await oauth2Client.getToken(code);
      oauth2Client.setCredentials(tokens);

      // Get channel information
      const channelResponse = await youtubeApi.channels.list({
        auth: oauth2Client,
        part: ["snippet", "statistics"],
        mine: true
      });

      const channelData = channelResponse.data.items?.[0];
      if (!channelData) {
        throw new Error("No channel found for this account");
      }

      const channelInfo: InsertYoutubeChannel = {
        userId,
        channelId: channelData.id!,
        channelTitle: channelData.snippet?.title || "Unknown Channel",
        accessToken: tokens.access_token!,
        refreshToken: tokens.refresh_token!,
        subscriberCount: parseInt(channelData.statistics?.subscriberCount || "0"),
        viewCount: parseInt(channelData.statistics?.viewCount || "0"),
        isActive: true
      };

      return channelInfo;
    } catch (error) {
      console.error("Error handling YouTube callback:", error);
      throw new Error("Failed to connect YouTube channel");
    }
  }

  async refreshTokens(channel: YoutubeChannel): Promise<void> {
    try {
      oauth2Client.setCredentials({
        access_token: channel.accessToken,
        refresh_token: channel.refreshToken
      });

      const { credentials } = await oauth2Client.refreshAccessToken();
      
      if (credentials.access_token) {
        await storage.updateChannel(channel.id, {
          accessToken: credentials.access_token,
          refreshToken: credentials.refresh_token || channel.refreshToken
        });
      }
    } catch (error) {
      console.error("Error refreshing YouTube tokens:", error);
      throw new Error("Failed to refresh YouTube tokens");
    }
  }

  async syncComments(channel: YoutubeChannel): Promise<Comment[]> {
    try {
      oauth2Client.setCredentials({
        access_token: channel.accessToken,
        refresh_token: channel.refreshToken
      });

      // Get channel's videos
      const videosResponse = await youtubeApi.search.list({
        auth: oauth2Client,
        part: ["id"],
        channelId: channel.channelId,
        type: "video",
        maxResults: 50,
        order: "date"
      });

      const newComments: Comment[] = [];

      // For each video, get comments
      for (const video of videosResponse.data.items || []) {
        if (!video.id?.videoId) continue;

        try {
          const commentsResponse = await youtubeApi.commentThreads.list({
            auth: oauth2Client,
            part: ["snippet", "replies"],
            videoId: video.id.videoId,
            maxResults: 100,
            order: "time"
          });

          for (const thread of commentsResponse.data.items || []) {
            const comment = thread.snippet?.topLevelComment?.snippet;
            if (!comment || !thread.id) continue;

            // Check if comment already exists
            const existingComments = await storage.getCommentsByChannelId(channel.id);
            const exists = existingComments.find(c => c.youtubeCommentId === thread.id);
            
            if (!exists) {
              const commentData: InsertComment = {
                channelId: channel.id,
                videoId: video.id.videoId,
                youtubeCommentId: thread.id,
                authorName: comment.authorDisplayName || "Unknown",
                authorAvatar: comment.authorProfileImageUrl || "",
                text: comment.textDisplay || "",
                likeCount: comment.likeCount || 0,
                publishedAt: new Date(comment.publishedAt || Date.now()),
                status: "unread"
              };

              const newComment = await storage.createComment(commentData);
              newComments.push(newComment);
            }
          }
        } catch (error) {
          console.error(`Error fetching comments for video ${video.id.videoId}:`, error);
          // Continue with other videos
        }
      }

      return newComments;
    } catch (error) {
      console.error("Error syncing comments:", error);
      
      // Try to refresh tokens if unauthorized
      if (error.code === 401) {
        await this.refreshTokens(channel);
        // Retry once with new tokens
        return this.syncComments(channel);
      }
      
      throw new Error("Failed to sync comments from YouTube");
    }
  }

  async replyToComment(channel: YoutubeChannel, commentId: string, text: string): Promise<string> {
    try {
      oauth2Client.setCredentials({
        access_token: channel.accessToken,
        refresh_token: channel.refreshToken
      });

      const response = await youtubeApi.comments.insert({
        auth: oauth2Client,
        part: ["snippet"],
        requestBody: {
          snippet: {
            parentId: commentId,
            textOriginal: text
          }
        }
      });

      return response.data.id!;
    } catch (error) {
      console.error("Error replying to comment:", error);
      
      // Try to refresh tokens if unauthorized
      if (error.code === 401) {
        await this.refreshTokens(channel);
        // Retry once with new tokens
        return this.replyToComment(channel, commentId, text);
      }
      
      throw new Error("Failed to reply to comment");
    }
  }

  async uploadVideo(channel: YoutubeChannel, videoData: {
    title: string;
    description: string;
    tags: string[];
    privacyStatus: string;
    videoFile: string;
  }): Promise<string> {
    try {
      oauth2Client.setCredentials({
        access_token: channel.accessToken,
        refresh_token: channel.refreshToken
      });

      const response = await youtubeApi.videos.insert({
        auth: oauth2Client,
        part: ["snippet", "status"],
        requestBody: {
          snippet: {
            title: videoData.title,
            description: videoData.description,
            tags: videoData.tags,
            categoryId: "22" // People & Blogs - default category
          },
          status: {
            privacyStatus: videoData.privacyStatus as any
          }
        },
        media: {
          body: require("fs").createReadStream(videoData.videoFile)
        }
      });

      return response.data.id!;
    } catch (error) {
      console.error("Error uploading video:", error);
      
      // Try to refresh tokens if unauthorized
      if (error.code === 401) {
        await this.refreshTokens(channel);
        // Retry once with new tokens
        return this.uploadVideo(channel, videoData);
      }
      
      throw new Error("Failed to upload video to YouTube");
    }
  }

  async updateVideoStatus(channel: YoutubeChannel, videoId: string, privacyStatus: string): Promise<void> {
    try {
      oauth2Client.setCredentials({
        access_token: channel.accessToken,
        refresh_token: channel.refreshToken
      });

      await youtubeApi.videos.update({
        auth: oauth2Client,
        part: ["status"],
        requestBody: {
          id: videoId,
          status: {
            privacyStatus: privacyStatus as any
          }
        }
      });
    } catch (error) {
      console.error("Error updating video status:", error);
      
      // Try to refresh tokens if unauthorized
      if (error.code === 401) {
        await this.refreshTokens(channel);
        // Retry once with new tokens
        return this.updateVideoStatus(channel, videoId, privacyStatus);
      }
      
      throw new Error("Failed to update video status");
    }
  }
}

export const youtube = new YouTubeService();
