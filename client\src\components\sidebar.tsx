import { Youtube, BarChart3, Video, MessageCircle, Bot, Settings, LogOut } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/hooks/use-auth";
import { cn } from "@/lib/utils";
import type { YoutubeChannel } from "@shared/schema";

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: any) => void;
  selectedChannelId: number | null;
  onChannelChange: (channelId: number) => void;
  channels: YoutubeChannel[];
}

export function Sidebar({ 
  activeSection, 
  onSectionChange, 
  selectedChannelId, 
  onChannelChange, 
  channels 
}: SidebarProps) {
  const { user, logoutMutation } = useAuth();

  const navigationItems = [
    { id: "dashboard", label: "Dashboard", icon: BarChart3 },
    { id: "videos", label: "Video Scheduler", icon: Video },
    { id: "comments", label: "Comment Management", icon: MessageCircle },
    { id: "automation", label: "AI Responses", icon: Bot },
    { id: "analytics", label: "Analytics", icon: BarChart3 },
    { id: "settings", label: "Settings", icon: Settings },
  ];

  return (
    <aside className="w-64 bg-white shadow-lg h-full border-r border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
            <Youtube className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900">YTAutoManager</h1>
            <p className="text-sm text-gray-600">Channel Management</p>
          </div>
        </div>
      </div>

      {/* Channel Selector */}
      {channels.length > 0 && (
        <div className="p-4 border-b border-gray-200">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Active Channel
          </label>
          <Select 
            value={selectedChannelId?.toString()} 
            onValueChange={(value) => onChannelChange(parseInt(value))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select channel" />
            </SelectTrigger>
            <SelectContent>
              {channels.map((channel) => (
                <SelectItem key={channel.id} value={channel.id.toString()}>
                  {channel.channelTitle}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Navigation */}
      <nav className="p-4 flex-1">
        <ul className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeSection === item.id;
            
            return (
              <li key={item.id}>
                <button
                  onClick={() => onSectionChange(item.id)}
                  className={cn(
                    "w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors",
                    isActive
                      ? "text-red-600 bg-red-50 border border-red-200"
                      : "text-gray-600 hover:bg-gray-50"
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <span className="font-medium">{item.label}</span>
                </button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3 mb-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src="" alt={user?.username} />
            <AvatarFallback>
              {user?.username?.slice(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="font-medium text-gray-900 text-sm truncate">
              {user?.username}
            </p>
            <p className="text-xs text-gray-600 truncate">
              Content Creator
            </p>
          </div>
        </div>
        <Button 
          variant="ghost" 
          size="sm" 
          className="w-full justify-start text-gray-600 hover:text-gray-900"
          onClick={() => logoutMutation.mutate()}
          disabled={logoutMutation.isPending}
        >
          <LogOut className="h-4 w-4 mr-2" />
          Sign Out
        </Button>
      </div>
    </aside>
  );
}
