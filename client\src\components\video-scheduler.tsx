import { useState, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { CloudUpload, Video, MoreVertical, Upload, X, CheckCircle } from "lucide-react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { formatDistanceToNow } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";

interface VideoSchedulerProps {
  channelId: number;
}

export function VideoScheduler({ channelId }: VideoSchedulerProps) {
  const { toast } = useToast();
  const videoFileRef = useRef<HTMLInputElement>(null);
  const thumbnailFileRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    tags: "",
    category: "Education",
    scheduledAt: "",
    privacy: "public",
    videoFile: "",
    thumbnailUrl: ""
  });

  const [uploadState, setUploadState] = useState({
    videoUploading: false,
    videoProgress: 0,
    videoUploaded: false,
    thumbnailUploading: false,
    thumbnailProgress: 0,
    thumbnailUploaded: false
  });

  const { data: scheduledVideos = [], isLoading } = useQuery({
    queryKey: ["/api/videos", channelId],
    enabled: !!channelId,
  });

  // Video upload mutation
  const uploadVideoMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('video', file);

      const response = await fetch('/api/upload/video', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to upload video');
      }

      return response.json();
    },
    onSuccess: (data) => {
      setFormData(prev => ({ ...prev, videoFile: data.filename }));
      setUploadState(prev => ({ ...prev, videoUploaded: true, videoUploading: false }));
      toast({
        title: "Video uploaded successfully",
        description: "Your video file has been uploaded.",
      });
    },
    onError: (error: Error) => {
      setUploadState(prev => ({ ...prev, videoUploading: false }));
      toast({
        title: "Failed to upload video",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Thumbnail upload mutation
  const uploadThumbnailMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('thumbnail', file);

      const response = await fetch('/api/upload/thumbnail', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to upload thumbnail');
      }

      return response.json();
    },
    onSuccess: (data) => {
      setFormData(prev => ({ ...prev, thumbnailUrl: data.filename }));
      setUploadState(prev => ({ ...prev, thumbnailUploaded: true, thumbnailUploading: false }));
      toast({
        title: "Thumbnail uploaded successfully",
        description: "Your thumbnail has been uploaded.",
      });
    },
    onError: (error: Error) => {
      setUploadState(prev => ({ ...prev, thumbnailUploading: false }));
      toast({
        title: "Failed to upload thumbnail",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const scheduleVideoMutation = useMutation({
    mutationFn: async (videoData: any) => {
      const res = await apiRequest("POST", "/api/videos", {
        ...videoData,
        channelId,
        scheduledAt: new Date(videoData.scheduledAt).toISOString()
      });
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "Video scheduled successfully",
        description: "Your video has been scheduled for publication.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/videos", channelId] });
      queryClient.invalidateQueries({ queryKey: ["/api/dashboard", channelId] });
      setFormData({
        title: "",
        description: "",
        tags: "",
        category: "Education",
        scheduledAt: "",
        privacy: "public",
        videoFile: "",
        thumbnailUrl: ""
      });
      setUploadState({
        videoUploading: false,
        videoProgress: 0,
        videoUploaded: false,
        thumbnailUploading: false,
        thumbnailProgress: 0,
        thumbnailUploaded: false
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to schedule video",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // File upload handlers
  const handleVideoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadState(prev => ({ ...prev, videoUploading: true }));
      uploadVideoMutation.mutate(file);
    }
  };

  const handleThumbnailUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadState(prev => ({ ...prev, thumbnailUploading: true }));
      uploadThumbnailMutation.mutate(file);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title || !formData.scheduledAt) {
      toast({
        title: "Missing required fields",
        description: "Please fill in the title and schedule date/time.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.videoFile) {
      toast({
        title: "Video file required",
        description: "Please upload a video file before scheduling.",
        variant: "destructive",
      });
      return;
    }

    scheduleVideoMutation.mutate(formData);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "scheduled":
        return <Badge variant="outline" className="text-green-600 border-green-600">Scheduled</Badge>;
      case "processing":
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Processing</Badge>;
      case "published":
        return <Badge variant="outline" className="text-gray-600 border-gray-600">Published</Badge>;
      case "failed":
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {/* Schedule New Video Form */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>Schedule New Video</CardTitle>
            <CardDescription>
              Upload and schedule your video for automatic publication
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Video Upload */}
              <div>
                <Label>Video File *</Label>
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer mt-2"
                  onClick={() => videoFileRef.current?.click()}
                >
                  {uploadState.videoUploading ? (
                    <div>
                      <Upload className="h-12 w-12 text-blue-500 mx-auto mb-4 animate-pulse" />
                      <p className="font-medium text-gray-900">Uploading video...</p>
                      <Progress value={uploadState.videoProgress} className="w-full mt-2" />
                    </div>
                  ) : uploadState.videoUploaded ? (
                    <div>
                      <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                      <p className="font-medium text-green-700">Video uploaded successfully</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          setUploadState(prev => ({ ...prev, videoUploaded: false }));
                          setFormData(prev => ({ ...prev, videoFile: "" }));
                        }}
                      >
                        <X className="h-4 w-4 mr-2" />
                        Remove
                      </Button>
                    </div>
                  ) : (
                    <div>
                      <CloudUpload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="font-medium text-gray-900">Drop your video file here or click to browse</p>
                      <p className="text-sm text-gray-600 mt-1">Supports MP4, MOV, AVI up to 100MB</p>
                    </div>
                  )}
                  <input
                    ref={videoFileRef}
                    type="file"
                    className="hidden"
                    accept="video/*"
                    onChange={handleVideoUpload}
                    disabled={uploadState.videoUploading}
                  />
                </div>
              </div>

              {/* Thumbnail Upload */}
              <div>
                <Label>Thumbnail (Optional)</Label>
                <div
                  className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer mt-2"
                  onClick={() => thumbnailFileRef.current?.click()}
                >
                  {uploadState.thumbnailUploading ? (
                    <div>
                      <Upload className="h-8 w-8 text-blue-500 mx-auto mb-2 animate-pulse" />
                      <p className="text-sm text-gray-900">Uploading thumbnail...</p>
                    </div>
                  ) : uploadState.thumbnailUploaded ? (
                    <div>
                      <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                      <p className="text-sm text-green-700">Thumbnail uploaded</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          setUploadState(prev => ({ ...prev, thumbnailUploaded: false }));
                          setFormData(prev => ({ ...prev, thumbnailUrl: "" }));
                        }}
                      >
                        <X className="h-3 w-3 mr-1" />
                        Remove
                      </Button>
                    </div>
                  ) : (
                    <div>
                      <CloudUpload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-900">Upload custom thumbnail</p>
                      <p className="text-xs text-gray-600 mt-1">JPG, PNG up to 5MB</p>
                    </div>
                  )}
                  <input
                    ref={thumbnailFileRef}
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleThumbnailUpload}
                    disabled={uploadState.thumbnailUploading}
                  />
                </div>
              </div>

              {/* Video Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="Enter video title"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData({ ...formData, category: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Education">Education</SelectItem>
                      <SelectItem value="Technology">Technology</SelectItem>
                      <SelectItem value="Entertainment">Entertainment</SelectItem>
                      <SelectItem value="Gaming">Gaming</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  rows={4}
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Enter video description"
                />
              </div>

              <div>
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  value={formData.tags}
                  onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                  placeholder="Enter tags separated by commas"
                />
              </div>

              {/* Schedule Settings */}
              <div>
                <Label htmlFor="scheduledAt">Publish Date & Time *</Label>
                <Input
                  id="scheduledAt"
                  type="datetime-local"
                  value={formData.scheduledAt}
                  onChange={(e) => setFormData({ ...formData, scheduledAt: e.target.value })}
                  required
                />
              </div>

              {/* Privacy Settings */}
              <div>
                <Label>Privacy</Label>
                <RadioGroup
                  value={formData.privacy}
                  onValueChange={(value) => setFormData({ ...formData, privacy: value })}
                  className="flex space-x-6 mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="public" id="public" />
                    <Label htmlFor="public">Public</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="unlisted" id="unlisted" />
                    <Label htmlFor="unlisted">Unlisted</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="private" id="private" />
                    <Label htmlFor="private">Private</Label>
                  </div>
                </RadioGroup>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 pt-6">
                <Button
                  type="submit"
                  className="bg-red-600 hover:bg-red-700"
                  disabled={scheduleVideoMutation.isPending}
                >
                  {scheduleVideoMutation.isPending ? "Scheduling..." : "Schedule Video"}
                </Button>
                <Button type="button" variant="outline">
                  Save as Draft
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>

      {/* Scheduled Videos List */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle>Scheduled Videos</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="border border-gray-200 rounded-lg p-4">
                    <Skeleton className="h-4 w-32 mb-2" />
                    <Skeleton className="h-3 w-24 mb-2" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                ))}
              </div>
            ) : scheduledVideos.length === 0 ? (
              <div className="text-center py-8">
                <Video className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No scheduled videos</p>
              </div>
            ) : (
              <div className="space-y-4">
                {scheduledVideos.map((video) => (
                  <div key={video.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 text-sm">{video.title}</h4>
                        <p className="text-gray-600 text-xs mt-1">
                          {formatDistanceToNow(new Date(video.scheduledAt), { addSuffix: true })}
                        </p>
                        <div className="flex items-center mt-2">
                          {getStatusBadge(video.status)}
                        </div>
                      </div>
                      <Button variant="ghost" size="icon" className="ml-4">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
