import OpenAI from "openai";
import { storage } from "./storage";

const openai = new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_ENV_VAR || "default_key"
});

export async function analyzeSentiment(text: string): Promise<{
  rating: number;
  confidence: number;
}> {
  try {
    const response = await openai.chat.completions.create({
      // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content:
            "You are a sentiment analysis expert. Analyze the sentiment of the text and provide a rating from 1 to 5 stars and a confidence score between 0 and 1. Respond with JSON in this format: { 'rating': number, 'confidence': number }",
        },
        {
          role: "user",
          content: text,
        },
      ],
      response_format: { type: "json_object" },
    });

    const result = JSON.parse(response.choices[0].message.content || "{}");

    return {
      rating: Math.max(1, Math.min(5, Math.round(result.rating || 3))),
      confidence: Math.max(0, Math.min(1, result.confidence || 0.5)),
    };
  } catch (error) {
    console.error("Failed to analyze sentiment:", error);
    // Return neutral sentiment as fallback
    return {
      rating: 3,
      confidence: 0.5,
    };
  }
}

export async function generateResponse(commentText: string, channelId: number): Promise<string> {
  try {
    // Get active AI rules for this channel
    const rules = await storage.getAiRulesByChannelId(channelId);
    const activeRules = rules.filter(rule => rule.isActive);

    // Check for keyword-based triggers first
    for (const rule of activeRules) {
      if (rule.triggerType === "keyword" && rule.triggers) {
        const keywords = rule.triggers as string[];
        const lowerComment = commentText.toLowerCase();
        
        if (keywords.some(keyword => lowerComment.includes(keyword.toLowerCase()))) {
          // Increment usage count
          await storage.updateAiRule(rule.id, {
            usageCount: rule.usageCount + 1
          });
          
          return rule.response;
        }
      }
    }

    // Check for sentiment-based rules
    const sentiment = await analyzeSentiment(commentText);
    
    for (const rule of activeRules) {
      if (rule.triggerType === "sentiment" && rule.triggers) {
        const triggers = rule.triggers as { sentiment: string; minConfidence: number };
        
        if (sentiment.confidence >= (triggers.minConfidence || 0.7)) {
          const sentimentType = sentiment.rating > 3 ? "positive" : sentiment.rating < 3 ? "negative" : "neutral";
          
          if (triggers.sentiment === sentimentType) {
            // Increment usage count
            await storage.updateAiRule(rule.id, {
              usageCount: rule.usageCount + 1
            });
            
            return rule.response;
          }
        }
      }
    }

    // Generate AI response using OpenAI
    const response = await openai.chat.completions.create({
      // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are a helpful YouTube creator responding to comments on your videos. 
                   Generate a friendly, engaging response to the comment. Keep it concise, 
                   personable, and encourage further engagement. Use appropriate emojis sparingly.
                   The response should be authentic and match the tone of a content creator.`,
        },
        {
          role: "user",
          content: `Comment: "${commentText}"\n\nGenerate a response:`,
        },
      ],
      max_tokens: 150,
      temperature: 0.7,
    });

    return response.choices[0].message.content || "Thanks for your comment! 😊";
  } catch (error) {
    console.error("Failed to generate AI response:", error);
    return "Thanks for your comment! 😊";
  }
}

export async function processAutomaticResponses(channelId: number): Promise<void> {
  try {
    // Get unread comments for this channel
    const comments = await storage.getCommentsByChannelId(channelId);
    const unreadComments = comments.filter(comment => comment.status === "unread");

    // Get AI rules for this channel
    const rules = await storage.getAiRulesByChannelId(channelId);
    const activeRules = rules.filter(rule => rule.isActive);

    for (const comment of unreadComments) {
      // Skip if comment is too old (more than 7 days)
      const daysSincePublished = (Date.now() - comment.publishedAt.getTime()) / (1000 * 60 * 60 * 24);
      if (daysSincePublished > 7) continue;

      let shouldRespond = false;
      let responseText = "";

      // Check keyword triggers
      for (const rule of activeRules) {
        if (rule.triggerType === "keyword" && rule.triggers) {
          const keywords = rule.triggers as string[];
          const lowerComment = comment.text.toLowerCase();
          
          if (keywords.some(keyword => lowerComment.includes(keyword.toLowerCase()))) {
            shouldRespond = true;
            responseText = rule.response;
            
            // Update rule usage
            await storage.updateAiRule(rule.id, {
              usageCount: rule.usageCount + 1
            });
            break;
          }
        }
      }

      // Check sentiment triggers if no keyword match
      if (!shouldRespond) {
        const sentiment = await analyzeSentiment(comment.text);
        
        for (const rule of activeRules) {
          if (rule.triggerType === "sentiment" && rule.triggers) {
            const triggers = rule.triggers as { sentiment: string; minConfidence: number };
            
            if (sentiment.confidence >= (triggers.minConfidence || 0.7)) {
              const sentimentType = sentiment.rating > 3 ? "positive" : sentiment.rating < 3 ? "negative" : "neutral";
              
              if (triggers.sentiment === sentimentType) {
                shouldRespond = true;
                responseText = rule.response;
                
                // Update rule usage
                await storage.updateAiRule(rule.id, {
                  usageCount: rule.usageCount + 1
                });
                break;
              }
            }
          }
        }
      }

      if (shouldRespond && responseText) {
        // Create automatic reply
        await storage.createCommentReply({
          commentId: comment.id,
          text: responseText,
          isAiGenerated: true
        });

        // Update comment status
        await storage.updateComment(comment.id, { status: "replied" });

        // Log the activity
        await storage.createActivityLog({
          channelId,
          type: "ai_response",
          description: `AI automatically responded to comment from ${comment.authorName}`,
          metadata: { commentId: comment.id, response: responseText }
        });
      }
    }
  } catch (error) {
    console.error("Error processing automatic responses:", error);
  }
}
