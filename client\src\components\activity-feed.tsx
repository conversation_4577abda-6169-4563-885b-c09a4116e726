import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Check<PERSON>ircle, Bo<PERSON>, AlertTriangle, Calendar } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDistanceToNow } from "date-fns";

interface ActivityFeedProps {
  channelId: number;
}

export function ActivityFeed({ channelId }: ActivityFeedProps) {
  const { data: dashboardData, isLoading } = useQuery({
    queryKey: ["/api/dashboard", channelId],
    enabled: !!channelId,
  });

  const activities = dashboardData?.recentActivity || [];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "video_published":
      case "video_scheduled":
        return { icon: CheckCircle, bgColor: "bg-green-100", iconColor: "text-green-600" };
      case "ai_response":
        return { icon: <PERSON><PERSON>, bgColor: "bg-blue-100", iconColor: "text-blue-600" };
      case "channel_connected":
        return { icon: Calendar, bgColor: "bg-red-100", iconColor: "text-red-600" };
      default:
        return { icon: AlertTriangle, bgColor: "bg-orange-100", iconColor: "text-orange-600" };
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-start space-x-4 p-4">
                <Skeleton className="w-10 h-10 rounded-lg" />
                <div className="flex-1">
                  <Skeleton className="h-4 w-48 mb-2" />
                  <Skeleton className="h-3 w-32 mb-1" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-6">
        <CardTitle className="text-lg font-semibold">Recent Activity</CardTitle>
        <Button variant="link" className="text-blue-600 hover:text-blue-700">
          View All
        </Button>
      </CardHeader>
      <CardContent>
        {activities.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-600">No recent activity</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activities.map((activity, index) => {
              const { icon: Icon, bgColor, iconColor } = getActivityIcon(activity.type);
              
              return (
                <div 
                  key={index}
                  className="flex items-start space-x-4 p-4 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className={`w-10 h-10 ${bgColor} rounded-lg flex items-center justify-center flex-shrink-0`}>
                    <Icon className={`h-5 w-5 ${iconColor}`} />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{activity.description}</p>
                    {activity.metadata && (
                      <p className="text-sm text-gray-600 mt-1">
                        {activity.metadata.title || activity.metadata.response || ""}
                      </p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDistanceToNow(new Date(activity.createdAt), { addSuffix: true })}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
