import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Separator } from "@/components/ui/separator";
import { Bot, MessageCircle, Percent, Plus, Edit, Trash2 } from "lucide-react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

interface AiAutomationProps {
  channelId: number;
}

const ruleFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  triggerType: z.enum(["keyword", "sentiment", "faq"]),
  triggers: z.any(),
  response: z.string().min(1, "Response is required"),
  confidenceThreshold: z.number().min(50).max(95),
  isActive: z.boolean(),
});

type RuleFormData = z.infer<typeof ruleFormSchema>;

export function AiAutomation({ channelId }: AiAutomationProps) {
  const { toast } = useToast();
  const [isRuleDialogOpen, setIsRuleDialogOpen] = useState(false);
  const [editingRule, setEditingRule] = useState<any>(null);
  const [keywords, setKeywords] = useState<string[]>([]);
  const [keywordInput, setKeywordInput] = useState("");

  const { data: aiRules = [], isLoading: rulesLoading } = useQuery({
    queryKey: ["/api/ai-rules", channelId],
    enabled: !!channelId,
  });

  const { data: dashboardData } = useQuery({
    queryKey: ["/api/dashboard", channelId],
    enabled: !!channelId,
  });

  const form = useForm<RuleFormData>({
    resolver: zodResolver(ruleFormSchema),
    defaultValues: {
      name: "",
      triggerType: "keyword",
      triggers: [],
      response: "",
      confidenceThreshold: 85,
      isActive: true,
    },
  });

  const createRuleMutation = useMutation({
    mutationFn: async (ruleData: any) => {
      const res = await apiRequest("POST", "/api/ai-rules", {
        ...ruleData,
        channelId,
      });
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: "AI rule created",
        description: "Your AI response rule has been created successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/ai-rules", channelId] });
      setIsRuleDialogOpen(false);
      form.reset();
      setKeywords([]);
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create rule",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateRuleMutation = useMutation({
    mutationFn: async ({ ruleId, updates }: { ruleId: number; updates: any }) => {
      const res = await apiRequest("PUT", `/api/ai-rules/${ruleId}`, updates);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/ai-rules", channelId] });
    },
  });

  const deleteRuleMutation = useMutation({
    mutationFn: async (ruleId: number) => {
      await apiRequest("DELETE", `/api/ai-rules/${ruleId}`);
    },
    onSuccess: () => {
      toast({
        title: "Rule deleted",
        description: "AI response rule has been deleted.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/ai-rules", channelId] });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete rule",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const addKeyword = () => {
    if (keywordInput.trim() && !keywords.includes(keywordInput.trim())) {
      setKeywords([...keywords, keywordInput.trim()]);
      setKeywordInput("");
    }
  };

  const removeKeyword = (keyword: string) => {
    setKeywords(keywords.filter(k => k !== keyword));
  };

  const onSubmit = (data: RuleFormData) => {
    let processedTriggers;
    
    if (data.triggerType === "keyword") {
      processedTriggers = keywords;
    } else if (data.triggerType === "sentiment") {
      processedTriggers = {
        sentiment: "positive", // Default for now
        minConfidence: data.confidenceThreshold / 100,
      };
    } else {
      processedTriggers = keywords; // FAQ uses keywords too
    }

    const ruleData = {
      ...data,
      triggers: processedTriggers,
    };

    if (editingRule) {
      updateRuleMutation.mutate({ ruleId: editingRule.id, updates: ruleData });
    } else {
      createRuleMutation.mutate(ruleData);
    }
  };

  const toggleRuleActive = (ruleId: number, isActive: boolean) => {
    updateRuleMutation.mutate({ ruleId, updates: { isActive } });
  };

  const openEditDialog = (rule: any) => {
    setEditingRule(rule);
    form.reset({
      name: rule.name,
      triggerType: rule.triggerType,
      response: rule.response,
      confidenceThreshold: rule.confidenceThreshold,
      isActive: rule.isActive,
    });
    
    if (rule.triggerType === "keyword" && Array.isArray(rule.triggers)) {
      setKeywords(rule.triggers);
    }
    
    setIsRuleDialogOpen(true);
  };

  const closeDialog = () => {
    setIsRuleDialogOpen(false);
    setEditingRule(null);
    form.reset();
    setKeywords([]);
  };

  const getTriggerTypeBadge = (triggerType: string) => {
    const colors = {
      keyword: "bg-blue-100 text-blue-600",
      sentiment: "bg-green-100 text-green-600",
      faq: "bg-purple-100 text-purple-600",
    };

    const labels = {
      keyword: "Keyword Trigger",
      sentiment: "Sentiment Analysis", 
      faq: "FAQ",
    };

    return (
      <Badge variant="outline" className={colors[triggerType as keyof typeof colors]}>
        {labels[triggerType as keyof typeof labels]}
      </Badge>
    );
  };

  // Calculate stats from dashboard data
  const stats = {
    activeRules: aiRules.filter(rule => rule.isActive).length,
    responsesToday: dashboardData?.stats?.pendingComments || 0,
    accuracyRate: 94, // This would come from actual AI metrics
  };

  return (
    <div className="space-y-6">
      {/* AI Settings Overview */}
      <Card>
        <CardHeader>
          <CardTitle>AI Response Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Bot className="h-8 w-8 text-green-600" />
              </div>
              <h4 className="font-medium text-gray-900">Active Rules</h4>
              <p className="text-2xl font-bold text-green-600 mt-1">{stats.activeRules}</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <MessageCircle className="h-8 w-8 text-blue-600" />
              </div>
              <h4 className="font-medium text-gray-900">Responses Today</h4>
              <p className="text-2xl font-bold text-blue-600 mt-1">{stats.responsesToday}</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Percent className="h-8 w-8 text-orange-600" />
              </div>
              <h4 className="font-medium text-gray-900">Accuracy Rate</h4>
              <p className="text-2xl font-bold text-orange-600 mt-1">{stats.accuracyRate}%</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Response Rules */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-6">
          <CardTitle>Response Rules</CardTitle>
          <Dialog open={isRuleDialogOpen} onOpenChange={setIsRuleDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-red-600 hover:bg-red-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Rule
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {editingRule ? "Edit AI Response Rule" : "Create AI Response Rule"}
                </DialogTitle>
                <DialogDescription>
                  Configure when and how the AI should respond to comments automatically.
                </DialogDescription>
              </DialogHeader>
              
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rule Name</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., FAQ: Video Resources" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="triggerType"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Trigger Type</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            value={field.value}
                            className="flex flex-col space-y-2"
                          >
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="keyword" id="keyword" />
                              <Label htmlFor="keyword">Keyword Trigger</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="sentiment" id="sentiment" />
                              <Label htmlFor="sentiment">Sentiment Analysis</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="faq" id="faq" />
                              <Label htmlFor="faq">FAQ Response</Label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch("triggerType") === "keyword" && (
                    <div>
                      <Label>Keywords</Label>
                      <div className="flex space-x-2 mt-2">
                        <Input
                          placeholder="Enter keyword"
                          value={keywordInput}
                          onChange={(e) => setKeywordInput(e.target.value)}
                          onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addKeyword())}
                        />
                        <Button type="button" onClick={addKeyword} variant="outline">
                          Add
                        </Button>
                      </div>
                      <div className="flex flex-wrap gap-2 mt-3">
                        {keywords.map((keyword) => (
                          <Badge key={keyword} variant="secondary" className="cursor-pointer">
                            {keyword}
                            <button
                              type="button"
                              onClick={() => removeKeyword(keyword)}
                              className="ml-2 text-red-500 hover:text-red-700"
                            >
                              ×
                            </button>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="response"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Response Text</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter the automatic response text..."
                            rows={4}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          This text will be posted as a reply when the trigger conditions are met.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="confidenceThreshold"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confidence Threshold: {field.value}%</FormLabel>
                        <FormControl>
                          <Slider
                            min={50}
                            max={95}
                            step={5}
                            value={[field.value]}
                            onValueChange={(value) => field.onChange(value[0])}
                            className="w-full"
                          />
                        </FormControl>
                        <FormDescription>
                          Minimum confidence level required to trigger this rule.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Active Rule</FormLabel>
                          <FormDescription>
                            Enable this rule to start automatic responses.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={closeDialog}>
                      Cancel
                    </Button>
                    <Button 
                      type="submit" 
                      className="bg-red-600 hover:bg-red-700"
                      disabled={createRuleMutation.isPending || updateRuleMutation.isPending}
                    >
                      {createRuleMutation.isPending || updateRuleMutation.isPending 
                        ? "Saving..." 
                        : editingRule ? "Update Rule" : "Create Rule"
                      }
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          {rulesLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="border border-gray-200 rounded-lg p-4">
                  <Skeleton className="h-4 w-48 mb-3" />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Skeleton className="h-16" />
                    <Skeleton className="h-16" />
                  </div>
                </div>
              ))}
            </div>
          ) : aiRules.length === 0 ? (
            <div className="text-center py-8">
              <Bot className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No AI response rules configured</p>
            </div>
          ) : (
            <div className="space-y-4">
              {aiRules.map((rule) => (
                <div key={rule.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${rule.isActive ? "bg-green-500" : "bg-gray-400"}`} />
                      <h4 className="font-medium text-gray-900">{rule.name}</h4>
                      {getTriggerTypeBadge(rule.triggerType)}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => openEditDialog(rule)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => deleteRuleMutation.mutate(rule.id)}
                        disabled={deleteRuleMutation.isPending}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600 font-medium mb-1">Triggers:</p>
                      <p className="text-gray-900">
                        {Array.isArray(rule.triggers) 
                          ? rule.triggers.join(", ")
                          : JSON.stringify(rule.triggers)
                        }
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-600 font-medium mb-1">Response:</p>
                      <p className="text-gray-900">{rule.response}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center space-x-4 text-xs">
                      <span className="text-gray-600">
                        Used: <span className="text-gray-900 font-medium">{rule.usageCount} times</span>
                      </span>
                      <span className="text-gray-600">
                        Success: <span className="text-green-600 font-medium">{rule.successRate}%</span>
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Switch
                        checked={rule.isActive}
                        onCheckedChange={(checked) => toggleRuleActive(rule.id, checked)}
                        disabled={updateRuleMutation.isPending}
                      />
                      <span className="ml-2 text-sm text-gray-600">Active</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* AI Settings and Templates */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <CardTitle>General Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Response Mode</Label>
              <Select defaultValue="suggest">
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="suggest">Suggest responses (Manual approval)</SelectItem>
                  <SelectItem value="auto-review">Auto-reply with review queue</SelectItem>
                  <SelectItem value="fully-auto">Fully automatic (Use with caution)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Default Confidence Threshold</Label>
              <div className="mt-2">
                <Slider defaultValue={[85]} min={50} max={95} step={5} className="w-full" />
                <div className="flex justify-between text-xs text-gray-600 mt-1">
                  <span>50%</span>
                  <span className="font-medium">85%</span>
                  <span>95%</span>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Enable sentiment analysis</Label>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <Label>Respond to positive comments</Label>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <Label>Flag negative comments for review</Label>
                <Switch defaultChecked />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Response Templates */}
        <Card>
          <CardHeader>
            <CardTitle>Response Templates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-900 mb-1">Thank You Response</p>
                <p className="text-xs text-gray-600">Thanks for watching! Don't forget to subscribe for more content! 🔔</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-900 mb-1">Question Follow-up</p>
                <p className="text-xs text-gray-600">Great question! I'll consider making a dedicated video about this topic.</p>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm font-medium text-gray-900 mb-1">Resource Request</p>
                <p className="text-xs text-gray-600">You can find all the resources and links in the video description! 📝</p>
              </div>
              <Button variant="outline" className="w-full mt-4">
                <Plus className="h-4 w-4 mr-2" />
                Add Template
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
