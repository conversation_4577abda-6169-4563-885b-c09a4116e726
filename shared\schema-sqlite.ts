import { sqliteTable, text, integer } from "drizzle-orm/sqlite-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

export const users = sqliteTable("users", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email").notNull().unique(),
  createdAt: text("created_at").default("CURRENT_TIMESTAMP").notNull(),
});

export const youtubeChannels = sqliteTable("youtube_channels", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  channelId: text("channel_id").notNull().unique(),
  channelTitle: text("channel_title").notNull(),
  accessToken: text("access_token").notNull(),
  refreshToken: text("refresh_token").notNull(),
  subscriberCount: integer("subscriber_count").default(0),
  viewCount: integer("view_count").default(0),
  isActive: integer("is_active", { mode: "boolean" }).default(true).notNull(),
  createdAt: text("created_at").default("CURRENT_TIMESTAMP").notNull(),
});

export const scheduledVideos = sqliteTable("scheduled_videos", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  channelId: integer("channel_id").notNull().references(() => youtubeChannels.id, { onDelete: "cascade" }),
  title: text("title").notNull(),
  description: text("description"),
  tags: text("tags"),
  thumbnailUrl: text("thumbnail_url"),
  videoFile: text("video_file"),
  youtubeVideoId: text("youtube_video_id"),
  scheduledAt: text("scheduled_at").notNull(),
  publishedAt: text("published_at"),
  status: text("status").notNull().default("scheduled"), // scheduled, processing, published, failed
  privacy: text("privacy").notNull().default("public"), // public, unlisted, private
  category: text("category"),
  createdAt: text("created_at").default("CURRENT_TIMESTAMP").notNull(),
});

export const comments = sqliteTable("comments", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  channelId: integer("channel_id").notNull().references(() => youtubeChannels.id, { onDelete: "cascade" }),
  videoId: text("video_id").notNull(),
  youtubeCommentId: text("youtube_comment_id").notNull().unique(),
  authorName: text("author_name").notNull(),
  authorAvatar: text("author_avatar"),
  text: text("text").notNull(),
  likeCount: integer("like_count").default(0),
  publishedAt: text("published_at").notNull(),
  sentiment: text("sentiment"), // positive, negative, neutral
  sentimentScore: integer("sentiment_score"), // 0-100
  status: text("status").notNull().default("unread"), // unread, read, replied, flagged
  parentCommentId: text("parent_comment_id"),
  createdAt: text("created_at").default("CURRENT_TIMESTAMP").notNull(),
});

export const commentReplies = sqliteTable("comment_replies", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  commentId: integer("comment_id").notNull().references(() => comments.id, { onDelete: "cascade" }),
  text: text("text").notNull(),
  youtubeReplyId: text("youtube_reply_id"),
  isAiGenerated: integer("is_ai_generated", { mode: "boolean" }).default(false),
  createdAt: text("created_at").default("CURRENT_TIMESTAMP").notNull(),
});

export const aiResponseRules = sqliteTable("ai_response_rules", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  channelId: integer("channel_id").notNull().references(() => youtubeChannels.id, { onDelete: "cascade" }),
  name: text("name").notNull(),
  triggerType: text("trigger_type").notNull(), // keyword, sentiment, faq
  triggers: text("triggers").notNull(), // JSON string of keywords or conditions
  response: text("response").notNull(),
  isActive: integer("is_active", { mode: "boolean" }).default(true).notNull(),
  confidenceThreshold: integer("confidence_threshold").default(85),
  usageCount: integer("usage_count").default(0),
  successRate: integer("success_rate").default(0),
  createdAt: text("created_at").default("CURRENT_TIMESTAMP").notNull(),
});

export const activityLogs = sqliteTable("activity_logs", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  channelId: integer("channel_id").notNull().references(() => youtubeChannels.id, { onDelete: "cascade" }),
  type: text("type").notNull(), // video_published, comment_replied, ai_response, etc.
  description: text("description").notNull(),
  metadata: text("metadata"), // JSON string
  createdAt: text("created_at").default("CURRENT_TIMESTAMP").notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  channels: many(youtubeChannels),
}));

export const youtubeChannelsRelations = relations(youtubeChannels, ({ one, many }) => ({
  user: one(users, {
    fields: [youtubeChannels.userId],
    references: [users.id],
  }),
  scheduledVideos: many(scheduledVideos),
  comments: many(comments),
  aiResponseRules: many(aiResponseRules),
  activityLogs: many(activityLogs),
}));

export const scheduledVideosRelations = relations(scheduledVideos, ({ one }) => ({
  channel: one(youtubeChannels, {
    fields: [scheduledVideos.channelId],
    references: [youtubeChannels.id],
  }),
}));

export const commentsRelations = relations(comments, ({ one, many }) => ({
  channel: one(youtubeChannels, {
    fields: [comments.channelId],
    references: [youtubeChannels.id],
  }),
  replies: many(commentReplies),
}));

export const commentRepliesRelations = relations(commentReplies, ({ one }) => ({
  comment: one(comments, {
    fields: [commentReplies.commentId],
    references: [comments.id],
  }),
}));

export const aiResponseRulesRelations = relations(aiResponseRules, ({ one }) => ({
  channel: one(youtubeChannels, {
    fields: [aiResponseRules.channelId],
    references: [youtubeChannels.id],
  }),
}));

export const activityLogsRelations = relations(activityLogs, ({ one }) => ({
  channel: one(youtubeChannels, {
    fields: [activityLogs.channelId],
    references: [youtubeChannels.id],
  }),
}));

// Insert schemas
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  email: true,
});

export const insertYoutubeChannelSchema = createInsertSchema(youtubeChannels).omit({
  id: true,
  createdAt: true,
});

export const insertScheduledVideoSchema = createInsertSchema(scheduledVideos).omit({
  id: true,
  createdAt: true,
  publishedAt: true,
  youtubeVideoId: true,
});

export const insertCommentSchema = createInsertSchema(comments).omit({
  id: true,
  createdAt: true,
});

export const insertCommentReplySchema = createInsertSchema(commentReplies).omit({
  id: true,
  createdAt: true,
});

export const insertAiResponseRuleSchema = createInsertSchema(aiResponseRules).omit({
  id: true,
  createdAt: true,
  usageCount: true,
  successRate: true,
});

export const insertActivityLogSchema = createInsertSchema(activityLogs).omit({
  id: true,
  createdAt: true,
});

// Types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type YoutubeChannel = typeof youtubeChannels.$inferSelect;
export type InsertYoutubeChannel = z.infer<typeof insertYoutubeChannelSchema>;
export type ScheduledVideo = typeof scheduledVideos.$inferSelect;
export type InsertScheduledVideo = z.infer<typeof insertScheduledVideoSchema>;
export type Comment = typeof comments.$inferSelect;
export type InsertComment = z.infer<typeof insertCommentSchema>;
export type CommentReply = typeof commentReplies.$inferSelect;
export type InsertCommentReply = z.infer<typeof insertCommentReplySchema>;
export type AiResponseRule = typeof aiResponseRules.$inferSelect;
export type InsertAiResponseRule = z.infer<typeof insertAiResponseRuleSchema>;
export type ActivityLog = typeof activityLogs.$inferSelect;
export type InsertActivityLog = z.infer<typeof insertActivityLogSchema>;
