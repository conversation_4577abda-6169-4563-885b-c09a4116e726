CREATE TABLE `activity_logs` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`channel_id` integer NOT NULL,
	`type` text NOT NULL,
	`description` text NOT NULL,
	`metadata` text,
	`created_at` text DEFAULT 'CURRENT_TIMESTAMP' NOT NULL,
	FOREIGN KEY (`channel_id`) REFERENCES `youtube_channels`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `ai_response_rules` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`channel_id` integer NOT NULL,
	`name` text NOT NULL,
	`trigger_type` text NOT NULL,
	`triggers` text NOT NULL,
	`response` text NOT NULL,
	`is_active` integer DEFAULT true NOT NULL,
	`confidence_threshold` integer DEFAULT 85,
	`usage_count` integer DEFAULT 0,
	`success_rate` integer DEFAULT 0,
	`created_at` text DEFAULT 'CURRENT_TIMESTAMP' NOT NULL,
	<PERSON>OR<PERSON><PERSON><PERSON> KEY (`channel_id`) REFERENCES `youtube_channels`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `comment_replies` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`comment_id` integer NOT NULL,
	`text` text NOT NULL,
	`youtube_reply_id` text,
	`is_ai_generated` integer DEFAULT false,
	`created_at` text DEFAULT 'CURRENT_TIMESTAMP' NOT NULL,
	FOREIGN KEY (`comment_id`) REFERENCES `comments`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `comments` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`channel_id` integer NOT NULL,
	`video_id` text NOT NULL,
	`youtube_comment_id` text NOT NULL,
	`author_name` text NOT NULL,
	`author_avatar` text,
	`text` text NOT NULL,
	`like_count` integer DEFAULT 0,
	`published_at` text NOT NULL,
	`sentiment` text,
	`sentiment_score` integer,
	`status` text DEFAULT 'unread' NOT NULL,
	`parent_comment_id` text,
	`created_at` text DEFAULT 'CURRENT_TIMESTAMP' NOT NULL,
	FOREIGN KEY (`channel_id`) REFERENCES `youtube_channels`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `comments_youtube_comment_id_unique` ON `comments` (`youtube_comment_id`);--> statement-breakpoint
CREATE TABLE `scheduled_videos` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`channel_id` integer NOT NULL,
	`title` text NOT NULL,
	`description` text,
	`tags` text,
	`thumbnail_url` text,
	`video_file` text,
	`youtube_video_id` text,
	`scheduled_at` text NOT NULL,
	`published_at` text,
	`status` text DEFAULT 'scheduled' NOT NULL,
	`privacy` text DEFAULT 'public' NOT NULL,
	`category` text,
	`created_at` text DEFAULT 'CURRENT_TIMESTAMP' NOT NULL,
	FOREIGN KEY (`channel_id`) REFERENCES `youtube_channels`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`username` text NOT NULL,
	`password` text NOT NULL,
	`email` text NOT NULL,
	`created_at` text DEFAULT 'CURRENT_TIMESTAMP' NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `users_username_unique` ON `users` (`username`);--> statement-breakpoint
CREATE UNIQUE INDEX `users_email_unique` ON `users` (`email`);--> statement-breakpoint
CREATE TABLE `youtube_channels` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer NOT NULL,
	`channel_id` text NOT NULL,
	`channel_title` text NOT NULL,
	`access_token` text NOT NULL,
	`refresh_token` text NOT NULL,
	`subscriber_count` integer DEFAULT 0,
	`view_count` integer DEFAULT 0,
	`is_active` integer DEFAULT true NOT NULL,
	`created_at` text DEFAULT 'CURRENT_TIMESTAMP' NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `youtube_channels_channel_id_unique` ON `youtube_channels` (`channel_id`);