import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle as drizzleNeon } from 'drizzle-orm/neon-serverless';
import { drizzle as drizzleSqlite } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import ws from "ws";
import * as schema from "@shared/schema";
import * as schemaSqlite from "@shared/schema-sqlite";

// Check if we have a DATABASE_URL (production/Neon) or use SQLite (development)
const databaseUrl = process.env.DATABASE_URL;

let db: any;
let pool: any;

if (databaseUrl && databaseUrl.includes('postgresql')) {
  // Use Neon/PostgreSQL
  neonConfig.webSocketConstructor = ws;
  pool = new Pool({ connectionString: databaseUrl });
  db = drizzleNeon({ client: pool, schema });
} else {
  // Use SQLite for development
  const sqlite = new Database('youtube_automation.db');
  db = drizzleSqlite(sqlite, { schema: schemaSqlite });

  console.log("Using SQLite database for development");
}

export { db, pool };