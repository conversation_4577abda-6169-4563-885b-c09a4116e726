import {
  users,
  youtubeChannels,
  scheduledVideos,
  comments,
  commentReplies,
  aiResponseRules,
  activityLogs,
  type User,
  type InsertUser,
  type YoutubeChannel,
  type InsertYoutubeChannel,
  type ScheduledVideo,
  type InsertScheduledVideo,
  type Comment,
  type InsertComment,
  type CommentReply,
  type InsertCommentReply,
  type AiResponseRule,
  type InsertAiResponseRule,
  type ActivityLog,
  type InsertActivityLog
} from "@shared/schema-sqlite";
import { db } from "./db";
import { eq, desc, and, sql } from "drizzle-orm";
import session from "express-session";
import connectPg from "connect-pg-simple";
import { pool } from "./db";

const PostgresSessionStore = connectPg(session);

export interface IStorage {
  // User management
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // YouTube channel management
  getAllChannels(): Promise<YoutubeChannel[]>;
  getChannelsByUserId(userId: number): Promise<YoutubeChannel[]>;
  getChannelById(id: number): Promise<YoutubeChannel | undefined>;
  createChannel(channel: InsertYoutubeChannel): Promise<YoutubeChannel>;
  updateChannel(id: number, updates: Partial<YoutubeChannel>): Promise<YoutubeChannel | undefined>;
  deleteChannel(id: number): Promise<void>;

  // Video scheduling
  getScheduledVideosByChannelId(channelId: number): Promise<ScheduledVideo[]>;
  getScheduledVideoById(id: number): Promise<ScheduledVideo | undefined>;
  createScheduledVideo(video: InsertScheduledVideo): Promise<ScheduledVideo>;
  updateScheduledVideo(id: number, updates: Partial<ScheduledVideo>): Promise<ScheduledVideo | undefined>;
  deleteScheduledVideo(id: number): Promise<void>;

  // Comment management
  getCommentsByChannelId(channelId: number, limit?: number): Promise<Comment[]>;
  getCommentById(id: number): Promise<Comment | undefined>;
  createComment(comment: InsertComment): Promise<Comment>;
  updateComment(id: number, updates: Partial<Comment>): Promise<Comment | undefined>;

  // Comment replies
  getRepliesByCommentId(commentId: number): Promise<CommentReply[]>;
  createCommentReply(reply: InsertCommentReply): Promise<CommentReply>;

  // AI response rules
  getAiRulesByChannelId(channelId: number): Promise<AiResponseRule[]>;
  getAiRuleById(id: number): Promise<AiResponseRule | undefined>;
  createAiRule(rule: InsertAiResponseRule): Promise<AiResponseRule>;
  updateAiRule(id: number, updates: Partial<AiResponseRule>): Promise<AiResponseRule | undefined>;
  deleteAiRule(id: number): Promise<void>;

  // Activity logs
  getActivityLogsByChannelId(channelId: number, limit?: number): Promise<ActivityLog[]>;
  createActivityLog(log: InsertActivityLog): Promise<ActivityLog>;

  // Dashboard stats
  getDashboardStats(channelId: number): Promise<{
    totalViews: number;
    subscribers: number;
    scheduledVideos: number;
    pendingComments: number;
  }>;

  sessionStore: any;
}

export class DatabaseStorage implements IStorage {
  sessionStore: any;

  constructor() {
    this.sessionStore = new PostgresSessionStore({
      pool,
      createTableIfMissing: true
    });
  }

  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async getAllChannels(): Promise<YoutubeChannel[]> {
    return await db
      .select()
      .from(youtubeChannels)
      .orderBy(desc(youtubeChannels.createdAt));
  }

  async getChannelsByUserId(userId: number): Promise<YoutubeChannel[]> {
    return await db
      .select()
      .from(youtubeChannels)
      .where(eq(youtubeChannels.userId, userId))
      .orderBy(desc(youtubeChannels.createdAt));
  }

  async getChannelById(id: number): Promise<YoutubeChannel | undefined> {
    const [channel] = await db
      .select()
      .from(youtubeChannels)
      .where(eq(youtubeChannels.id, id));
    return channel || undefined;
  }

  async createChannel(channel: InsertYoutubeChannel): Promise<YoutubeChannel> {
    const [newChannel] = await db
      .insert(youtubeChannels)
      .values(channel)
      .returning();
    return newChannel;
  }

  async updateChannel(id: number, updates: Partial<YoutubeChannel>): Promise<YoutubeChannel | undefined> {
    const [updated] = await db
      .update(youtubeChannels)
      .set(updates)
      .where(eq(youtubeChannels.id, id))
      .returning();
    return updated || undefined;
  }

  async deleteChannel(id: number): Promise<void> {
    await db.delete(youtubeChannels).where(eq(youtubeChannels.id, id));
  }

  async getScheduledVideosByChannelId(channelId: number): Promise<ScheduledVideo[]> {
    return await db
      .select()
      .from(scheduledVideos)
      .where(eq(scheduledVideos.channelId, channelId))
      .orderBy(desc(scheduledVideos.scheduledAt));
  }

  async getScheduledVideoById(id: number): Promise<ScheduledVideo | undefined> {
    const [video] = await db
      .select()
      .from(scheduledVideos)
      .where(eq(scheduledVideos.id, id));
    return video || undefined;
  }

  async createScheduledVideo(video: InsertScheduledVideo): Promise<ScheduledVideo> {
    const [newVideo] = await db
      .insert(scheduledVideos)
      .values(video)
      .returning();
    return newVideo;
  }

  async updateScheduledVideo(id: number, updates: Partial<ScheduledVideo>): Promise<ScheduledVideo | undefined> {
    const [updated] = await db
      .update(scheduledVideos)
      .set(updates)
      .where(eq(scheduledVideos.id, id))
      .returning();
    return updated || undefined;
  }

  async deleteScheduledVideo(id: number): Promise<void> {
    await db.delete(scheduledVideos).where(eq(scheduledVideos.id, id));
  }

  async getCommentsByChannelId(channelId: number, limit = 50): Promise<Comment[]> {
    return await db
      .select()
      .from(comments)
      .where(eq(comments.channelId, channelId))
      .orderBy(desc(comments.publishedAt))
      .limit(limit);
  }

  async getCommentById(id: number): Promise<Comment | undefined> {
    const [comment] = await db
      .select()
      .from(comments)
      .where(eq(comments.id, id));
    return comment || undefined;
  }

  async createComment(comment: InsertComment): Promise<Comment> {
    const [newComment] = await db
      .insert(comments)
      .values(comment)
      .returning();
    return newComment;
  }

  async updateComment(id: number, updates: Partial<Comment>): Promise<Comment | undefined> {
    const [updated] = await db
      .update(comments)
      .set(updates)
      .where(eq(comments.id, id))
      .returning();
    return updated || undefined;
  }

  async getRepliesByCommentId(commentId: number): Promise<CommentReply[]> {
    return await db
      .select()
      .from(commentReplies)
      .where(eq(commentReplies.commentId, commentId))
      .orderBy(desc(commentReplies.createdAt));
  }

  async createCommentReply(reply: InsertCommentReply): Promise<CommentReply> {
    const [newReply] = await db
      .insert(commentReplies)
      .values(reply)
      .returning();
    return newReply;
  }

  async getAiRulesByChannelId(channelId: number): Promise<AiResponseRule[]> {
    return await db
      .select()
      .from(aiResponseRules)
      .where(eq(aiResponseRules.channelId, channelId))
      .orderBy(desc(aiResponseRules.createdAt));
  }

  async getAiRuleById(id: number): Promise<AiResponseRule | undefined> {
    const [rule] = await db
      .select()
      .from(aiResponseRules)
      .where(eq(aiResponseRules.id, id));
    return rule || undefined;
  }

  async createAiRule(rule: InsertAiResponseRule): Promise<AiResponseRule> {
    const [newRule] = await db
      .insert(aiResponseRules)
      .values(rule)
      .returning();
    return newRule;
  }

  async updateAiRule(id: number, updates: Partial<AiResponseRule>): Promise<AiResponseRule | undefined> {
    const [updated] = await db
      .update(aiResponseRules)
      .set(updates)
      .where(eq(aiResponseRules.id, id))
      .returning();
    return updated || undefined;
  }

  async deleteAiRule(id: number): Promise<void> {
    await db.delete(aiResponseRules).where(eq(aiResponseRules.id, id));
  }

  async getActivityLogsByChannelId(channelId: number, limit = 20): Promise<ActivityLog[]> {
    return await db
      .select()
      .from(activityLogs)
      .where(eq(activityLogs.channelId, channelId))
      .orderBy(desc(activityLogs.createdAt))
      .limit(limit);
  }

  async createActivityLog(log: InsertActivityLog): Promise<ActivityLog> {
    const [newLog] = await db
      .insert(activityLogs)
      .values(log)
      .returning();
    return newLog;
  }

  async getDashboardStats(channelId: number): Promise<{
    totalViews: number;
    subscribers: number;
    scheduledVideos: number;
    pendingComments: number;
  }> {
    const [channel] = await db
      .select({
        viewCount: youtubeChannels.viewCount,
        subscriberCount: youtubeChannels.subscriberCount,
      })
      .from(youtubeChannels)
      .where(eq(youtubeChannels.id, channelId));

    const [scheduledCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(scheduledVideos)
      .where(
        and(
          eq(scheduledVideos.channelId, channelId),
          eq(scheduledVideos.status, "scheduled")
        )
      );

    const [pendingCommentsCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(comments)
      .where(
        and(
          eq(comments.channelId, channelId),
          eq(comments.status, "unread")
        )
      );

    return {
      totalViews: channel?.viewCount || 0,
      subscribers: channel?.subscriberCount || 0,
      scheduledVideos: scheduledCount?.count || 0,
      pendingComments: pendingCommentsCount?.count || 0,
    };
  }
}

export const storage = new DatabaseStorage();
